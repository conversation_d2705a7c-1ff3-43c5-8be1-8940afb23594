# AiLex Centralized Agent Configuration System

## Overview

The AiLex Centralized Agent Configuration System provides comprehensive management of all AI agents in the platform, including prompt management, model selection, environment-specific configurations, and testing capabilities.

## Features

### 🤖 **Agent Registry**
- Automatic agent discovery from codebase
- Centralized configuration management
- Support for agent-level and node-level configurations
- Environment-specific overrides

### 🎯 **Prompt Management**
- Integration with existing superadmin prompt system
- Version control for prompts
- Agent and node-specific prompt configurations
- Template variable support

### 🧠 **Model Configuration**
- Integration with existing superadmin LLM selection system
- Support for multiple providers (OpenAI, Anthropic, Google, etc.)
- Temperature, token limits, and other model parameters
- Inheritance from agent to node level

### 🌍 **Environment Support**
- Development, staging, and production configurations
- Environment-specific feature flags
- Resource limits and monitoring settings
- Configuration validation

### 🧪 **Testing & Evaluation**
- Sandbox environment for safe testing
- Test case management and execution
- Performance metrics and evaluation
- Automated testing suites

## Architecture

```
backend/agents/config/
├── __init__.py              # Main exports
├── models.py                # Pydantic models
├── registry.py              # Agent discovery and registration
├── loader.py                # Configuration loading
├── validator.py             # Configuration validation
├── environments.py          # Environment management
├── testing.py               # Testing framework
├── agents/                  # Agent configurations
│   ├── research_agent.yaml
│   ├── intake_agent.yaml
│   ├── supervisor_agent.yaml
│   └── task_crud_agent.yaml
└── environments/            # Environment configurations
    ├── development.yaml
    ├── staging.yaml
    └── production.yaml
```

## Usage

### Basic Usage

```python
from backend.agents.config import get_agent_config, AgentRegistry

# Get agent configuration
config = get_agent_config("research_agent", tenant_id="tenant-123")

# Get all agents
registry = AgentRegistry()
agents = registry.get_all_agents()
```

### Environment Configuration

```python
from backend.agents.config import get_environment_config, apply_environment_overrides

# Get current environment config
env_config = get_environment_config()

# Apply environment overrides to agent config
final_config = apply_environment_overrides(agent_config)
```

### Testing

```python
from backend.agents.config import AgentTester, TestSuite

# Quick test
tester = AgentTester()
result = await tester.quick_test(
    agent_config,
    {"message": "Test input"},
    {"expected": "output"}
)

# Create test suite
suite = TestSuite("research_tests", "Research agent test suite")
suite.add_test_case(test_case)
results = await suite.run_all_tests(agent_config)
```

## Configuration Files

### Agent Configuration (YAML)

```yaml
name: research_agent
display_name: Research Agent
description: Legal research agent for finding relevant laws
agent_type: interactive
version: 1.0.0
icon: 🔍

# Agent-level configurations
prompt_config:
  key: research_agent_system_prompt
  content: |
    You are a legal research assistant...
  description: System prompt for research agent
  tags: ["research", "legal"]
  version: 1

model_config:
  provider: openai
  model_name: openai/gpt-4
  temperature: 0.3
  max_tokens: 3000

# Node configurations
nodes:
  query_gen:
    name: query_gen
    display_name: Query Generation
    description: Generate optimized search queries
    node_type: generator
    # Node-specific prompt and model configs...

capabilities:
  - legal_research
  - document_search
  - citation_formatting

tools:
  - search_documents
  - format_citations

test_cases:
  - id: basic_research_test
    name: Basic Research Test
    # Test case configuration...

is_enabled: true
is_experimental: false
```

### Environment Configuration (YAML)

```yaml
name: production
description: Production environment configuration

default_model_config:
  provider: openai
  model_name: openai/gpt-4
  temperature: 0.1

agent_overrides:
  research_agent:
    model_config:
      temperature: 0.2
    metadata:
      enable_caching: true

feature_flags:
  enable_debug_mode: false
  enable_experimental_features: false

max_tokens_per_request: 4000
max_requests_per_minute: 30
timeout_seconds: 30
```

## Superadmin Interface

### Agent Configuration Page (`/superadmin/agents`)

- **Agent Overview**: View all agents with their configurations
- **Model Management**: Configure models for agents and nodes
- **Prompt Management**: Edit prompts with syntax highlighting
- **Status Control**: Enable/disable agents and nodes
- **Environment Settings**: Switch between environments

### Testing Sandbox (`/superadmin/agents/testing`)

- **Quick Test**: Run ad-hoc tests against any agent
- **Test Cases**: Manage predefined test cases
- **Results Dashboard**: View test results and metrics
- **Performance Analytics**: Monitor agent performance

## API Endpoints

### Agent Configuration API

```typescript
// Get all agent configurations
GET /api/admin/agent-configs
Query params: agent_type, enabled, search

// Update agent configuration
POST /api/admin/agent-configs
Body: {
  agent_name: string,
  node_name?: string,
  config_type: 'prompt' | 'model',
  config_data: object
}
```

### Testing API

```typescript
// Get test cases and results
GET /api/admin/agent-testing
Query params: agent_name, include_results

// Execute tests or create test cases
POST /api/admin/agent-testing
Body: {
  action: 'execute_tests' | 'create_test_case',
  // Additional parameters based on action
}
```

## Integration with Existing Systems

### Superadmin Prompt Management
- Seamless integration with existing `/superadmin/prompts` interface
- Automatic synchronization of prompt configurations
- Version control and change tracking

### Superadmin LLM Selection
- Integration with existing `/superadmin/models` interface
- Agent and node-level model configurations
- Provider and model validation

### Security and Logging
- All configuration changes are logged for security auditing
- Integration with existing security event logging
- Role-based access control (superadmin only)

## Environment Variables

```bash
# Environment detection
AILEX_ENVIRONMENT=development|staging|production

# Agent-specific overrides
AILEX_AGENT_RESEARCH_TEMPERATURE=0.3
AILEX_AGENT_INTAKE_MAX_TOKENS=2000
```

## Best Practices

### Configuration Management
1. **Use YAML files** for static configurations
2. **Database overrides** for dynamic superadmin changes
3. **Environment variables** for deployment-specific settings
4. **Version control** all configuration files

### Testing
1. **Write test cases** for all agents
2. **Use sandbox environment** for safe testing
3. **Monitor performance metrics** regularly
4. **Validate configurations** before deployment

### Security
1. **Never store secrets** in configuration files
2. **Use environment variables** for sensitive data
3. **Validate all inputs** before applying configurations
4. **Log all configuration changes** for audit trails

## Troubleshooting

### Common Issues

1. **Agent not found**: Check agent registry and configuration files
2. **Configuration validation errors**: Review YAML syntax and required fields
3. **Model selection issues**: Verify provider and model name format
4. **Test failures**: Check sandbox environment and test case definitions

### Debugging

```python
# Enable debug logging
import logging
logging.getLogger('backend.agents.config').setLevel(logging.DEBUG)

# Validate configuration
from backend.agents.config import validate_agent_config
result = validate_agent_config(config)
print(result.errors, result.warnings)
```

## Contributing

1. **Add new agents**: Create YAML configuration files in `agents/` directory
2. **Extend functionality**: Follow existing patterns and add tests
3. **Update documentation**: Keep README and docstrings current
4. **Test thoroughly**: Use the testing framework for validation

## Support

For questions or issues with the agent configuration system:
- **Documentation**: https://docs.ailex.com/agents/configuration
- **Support**: <EMAIL>
- **Issues**: Create tickets in the main repository
