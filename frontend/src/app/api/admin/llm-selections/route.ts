import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server-new';
import { isAuthenticatedLegacy as isAuthenticated, hasRoleLegacy as hasRole, UserRole } from '@/lib/auth/server-exports';
import { logSecurityEvent } from '@/lib/security/forensics';

// Define LLM selection interface
export interface LLMSelection {
  id: string;
  tenant_id: string;
  agent: string;
  node: string | null;
  model_name: string;
  temperature: number;
  created_at?: string;
  updated_at?: string;
}

// Define agent configuration interface
export interface AgentConfig {
  name: string;
  displayName: string;
  icon: string;
  nodes: NodeConfig[];
  currentModel?: string;
  temperature?: number;
  isConfigured: boolean;
  inheritsFrom?: string;
  capabilities?: string[];
  description?: string;
  agentType?: string;
  version?: string;
}

export interface NodeConfig {
  name: string;
  displayName: string;
  currentModel?: string;
  temperature?: number;
  isConfigured: boolean;
  inheritsFrom?: string;
}

// Enhanced agent definitions with centralized configuration integration
const AGENT_DEFINITIONS: Record<string, {
  displayName: string;
  icon: string;
  nodes: Record<string, string>;
  description?: string;
  agentType?: string;
  capabilities?: string[];
}> = {
  researchAgent: {
    displayName: 'Research Agent',
    icon: '🔍',
    description: 'Legal research agent for finding relevant laws, statutes, and case precedents',
    agentType: 'interactive',
    capabilities: ['legal_research', 'document_search', 'citation_formatting'],
    nodes: {
      query_gen: 'Query Generation',
      rerank: 'Result Reranking',
      exit_guard: 'Exit Guard'
    }
  },
  intakeAgent: {
    displayName: 'Intake Agent',
    icon: '📝',
    description: 'Multi-practice intake agent for Personal Injury, Family Law, and Criminal Defense cases',
    agentType: 'interactive',
    capabilities: ['client_intake', 'case_classification', 'conflict_checking'],
    nodes: {
      classify_practice_area: 'Practice Area Classification',
      collect_case_details: 'Case Details Collection',
      assess_urgency: 'Urgency Assessment',
      check_conflicts: 'Conflict Check'
    }
  },
  supervisorAgent: {
    displayName: 'Supervisor Agent',
    icon: '👥',
    description: 'Main routing and coordination agent for the AiLex platform',
    agentType: 'insights',
    capabilities: ['intent_routing', 'task_coordination', 'agent_supervision'],
    nodes: {}
  },
  calendarCrudAgent: {
    displayName: 'Calendar CRUD Agent',
    icon: '📅',
    description: 'Calendar management agent with Google/Outlook integration',
    agentType: 'interactive',
    capabilities: ['calendar_management', 'scheduling', 'conflict_detection'],
    nodes: {}
  },
  taskCrudAgent: {
    displayName: 'Task CRUD Agent',
    icon: '✅',
    description: 'Task management agent for creating, reading, updating, and deleting tasks',
    agentType: 'interactive',
    capabilities: ['task_management', 'crud_operations', 'natural_language_parsing'],
    nodes: {
      create_task: 'Create Task',
      read_task: 'Read Tasks',
      update_task: 'Update Task',
      delete_task: 'Delete Task'
    }
  },
  caseCrudAgent: {
    displayName: 'Case & Client CRUD Agent',
    icon: '⚖️',
    description: 'Case and client management agent for legal practice operations',
    agentType: 'interactive',
    capabilities: ['case_management', 'client_management', 'data_operations'],
    nodes: {
      router: 'Router',
      create_case: 'Create Case',
      read_case: 'Read Case',
      update_case: 'Update Case',
      delete_case: 'Delete Case',
      create_client: 'Create Client',
      read_client: 'Read Client',
      update_client: 'Update Client',
      delete_client: 'Delete Client'
    }
  }
};

/**
 * GET /api/admin/llm-selections
 * Get all LLM selections with agent/node structure
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = createClient();
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = session.user;

    if (!isAuthenticated(user) || !hasRole(user, [UserRole.Superadmin])) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const tenant = searchParams.get('tenant') || '*';
    const search = searchParams.get('search') || '';
    const provider = searchParams.get('provider');

    // Get all LLM selections
    const { data: selections, error: selectionsError } = await (supabase as any)
      .schema('security')
      .from('admin_llm_selection')
      .select('*')
      .eq('tenant_id', tenant);

    // Type the selections properly
    const typedSelections = (selections || []) as unknown as LLMSelection[];

    if (selectionsError) {
      console.error('Error fetching LLM selections:', selectionsError);
      return NextResponse.json({ error: 'Failed to fetch LLM selections' }, { status: 500 });
    }

    // Build agent configurations
    const agentConfigs: AgentConfig[] = [];

    for (const [agentName, agentDef] of Object.entries(AGENT_DEFINITIONS)) {
      // Skip if search doesn't match
      if (search && !agentName.toLowerCase().includes(search.toLowerCase()) &&
          !agentDef.displayName.toLowerCase().includes(search.toLowerCase())) {
        continue;
      }

      // Find agent-level selection
      const agentSelection = typedSelections.find(s => s.agent === agentName && s.node === null);

      // Build node configurations
      const nodeConfigs: NodeConfig[] = [];
      for (const [nodeName, nodeDisplayName] of Object.entries(agentDef.nodes)) {
        const nodeSelection = typedSelections.find(s => s.agent === agentName && s.node === nodeName);

        nodeConfigs.push({
          name: nodeName,
          displayName: nodeDisplayName,
          currentModel: nodeSelection?.model_name,
          temperature: nodeSelection?.temperature,
          isConfigured: !!nodeSelection,
          inheritsFrom: nodeSelection ? undefined : (agentSelection ? 'agent' : 'default')
        });
      }

      // Filter by provider if specified
      if (provider) {
        const hasProviderMatch = (agentSelection?.model_name?.startsWith(provider + '/')) ||
          nodeConfigs.some(node => node.currentModel?.startsWith(provider + '/'));

        if (!hasProviderMatch) {
          continue;
        }
      }

      agentConfigs.push({
        name: agentName,
        displayName: agentDef.displayName,
        icon: agentDef.icon,
        nodes: nodeConfigs,
        currentModel: agentSelection?.model_name,
        temperature: agentSelection?.temperature,
        isConfigured: !!agentSelection,
        inheritsFrom: agentSelection ? undefined : 'default',
        capabilities: agentDef.capabilities,
        description: agentDef.description,
        agentType: agentDef.agentType,
        version: '1.0.0'
      });
    }

    return NextResponse.json({
      agents: agentConfigs,
      selections: typedSelections
    });
  } catch (error) {
    console.error('Error in LLM selections API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * POST /api/admin/llm-selections
 * Create or update an LLM selection
 */
export async function POST(request: NextRequest) {
  try {
    const supabase = createClient();
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = session.user;

    if (!isAuthenticated(user) || !hasRole(user, [UserRole.Superadmin])) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await request.json();
    const { tenant_id, agent, node, model_name, temperature } = body;

    // Validate required fields
    if (!tenant_id || !agent || !model_name) {
      return NextResponse.json({
        error: 'tenant_id, agent, and model_name are required'
      }, { status: 400 });
    }

    // Validate agent exists
    if (!AGENT_DEFINITIONS[agent]) {
      return NextResponse.json({
        error: `Unknown agent: ${agent}`
      }, { status: 400 });
    }

    // Validate node exists for agent (if node is specified)
    if (node && !AGENT_DEFINITIONS[agent].nodes[node]) {
      return NextResponse.json({
        error: `Unknown node '${node}' for agent '${agent}'`
      }, { status: 400 });
    }

    // Upsert the selection
    const { data, error } = await (supabase as any)
      .schema('security')
      .from('admin_llm_selection')
      .upsert({
        tenant_id,
        agent,
        node,
        model_name,
        temperature: temperature || 0.2
      })
      .select()
      .single();

    if (error) {
      console.error('Error upserting LLM selection:', error);
      return NextResponse.json({ error: 'Failed to save LLM selection' }, { status: 500 });
    }

    // Log the security event
    await logSecurityEvent(supabase, 'llm_selection.updated', {
      tenant_id,
      agent,
      node,
      model_name,
      temperature,
      userId: user.id,
      userEmail: user.email
    });

    return NextResponse.json({ success: true, data });
  } catch (error) {
    console.error('Error in LLM selections API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * DELETE /api/admin/llm-selections
 * Delete an LLM selection
 */
export async function DELETE(request: NextRequest) {
  try {
    const supabase = createClient();
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = session.user;

    if (!isAuthenticated(user) || !hasRole(user, [UserRole.Superadmin])) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const searchParams = request.nextUrl.searchParams;
    const tenant_id = searchParams.get('tenant_id');
    const agent = searchParams.get('agent');
    const node = searchParams.get('node');

    // Validate required fields
    if (!tenant_id || !agent) {
      return NextResponse.json({
        error: 'tenant_id and agent are required'
      }, { status: 400 });
    }

    // Build the delete query
    const { data, error } = node
      ? await (supabase as any)
          .schema('security')
          .from('admin_llm_selection')
          .delete()
          .eq('tenant_id', tenant_id)
          .eq('agent', agent)
          .eq('node', node)
      : await (supabase as any)
          .schema('security')
          .from('admin_llm_selection')
          .delete()
          .eq('tenant_id', tenant_id)
          .eq('agent', agent)
          .is('node', null);

    if (error) {
      console.error('Error deleting LLM selection:', error);
      return NextResponse.json({ error: 'Failed to delete LLM selection' }, { status: 500 });
    }

    // Log the security event
    await logSecurityEvent(supabase, 'llm_selection.deleted', {
      tenant_id,
      agent,
      node,
      userId: user.id,
      userEmail: user.email
    });

    return NextResponse.json({ success: true, deleted: (data as any)?.length || 0 });
  } catch (error) {
    console.error('Error in LLM selections API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
