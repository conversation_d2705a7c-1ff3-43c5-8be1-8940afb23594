"""
Agent Registry System

This module provides centralized agent discovery, registration, and configuration
management. It automatically discovers agents from the codebase and maintains
a registry of all available agents with their configurations.
"""

import os
import importlib
import inspect
import logging
from typing import Dict, List, Optional, Any, Type, Union
from pathlib import Path
from functools import lru_cache

from .models import AgentConfigModel, NodeConfigModel, PromptConfigModel, ModelConfigModel
from .loader import ConfigLoader
from .validator import ConfigValidator

logger = logging.getLogger(__name__)


class AgentRegistry:
    """
    Centralized registry for all AiLex agents.
    
    Provides automatic agent discovery, configuration management,
    and integration with superadmin systems.
    """
    
    def __init__(self):
        """Initialize the agent registry."""
        self._agents: Dict[str, AgentConfigModel] = {}
        self._config_loader = ConfigLoader()
        self._validator = ConfigValidator()
        self._discovered_agents: Dict[str, Type] = {}
        
        # Auto-discover agents on initialization
        self._discover_agents()
        self._load_configurations()
    
    def _discover_agents(self) -> None:
        """
        Automatically discover all agents in the codebase.
        
        Scans the backend/agents directory structure to find all agent classes
        and their node configurations.
        """
        logger.info("Starting agent discovery...")
        
        # Get the base agents directory
        agents_dir = Path(__file__).parent.parent
        
        # Discover interactive agents
        self._discover_agents_in_directory(
            agents_dir / "interactive",
            "backend.agents.interactive",
            "interactive"
        )
        
        # Discover insights agents
        self._discover_agents_in_directory(
            agents_dir / "insights", 
            "backend.agents.insights",
            "insights"
        )
        
        # Discover matter/client agents
        self._discover_agents_in_directory(
            agents_dir / "matter_client",
            "backend.agents.matter_client", 
            "interactive"
        )
        
        logger.info(f"Discovered {len(self._discovered_agents)} agents")
    
    def _discover_agents_in_directory(
        self, 
        directory: Path, 
        module_prefix: str,
        agent_type: str
    ) -> None:
        """
        Discover agents in a specific directory.
        
        Args:
            directory: Directory to scan
            module_prefix: Python module prefix
            agent_type: Type of agents in this directory
        """
        if not directory.exists():
            return
            
        for item in directory.iterdir():
            if item.is_dir() and not item.name.startswith('_'):
                agent_name = item.name
                
                # Try to import the agent module
                try:
                    module_path = f"{module_prefix}.{agent_name}"
                    
                    # Look for agent.py or __init__.py
                    agent_file = item / "agent.py"
                    if not agent_file.exists():
                        agent_file = item / "__init__.py"
                    
                    if agent_file.exists():
                        module = importlib.import_module(module_path)
                        
                        # Find agent classes in the module
                        agent_classes = self._find_agent_classes(module)
                        
                        for agent_class in agent_classes:
                            self._register_discovered_agent(
                                agent_name,
                                agent_class,
                                agent_type,
                                item
                            )
                            
                except Exception as e:
                    logger.warning(f"Failed to discover agent {agent_name}: {e}")
    
    def _find_agent_classes(self, module) -> List[Type]:
        """
        Find agent classes in a module.
        
        Args:
            module: Python module to scan
            
        Returns:
            List of agent classes found
        """
        agent_classes = []
        
        for name, obj in inspect.getmembers(module, inspect.isclass):
            # Check if it's an agent class (has common agent patterns)
            if (hasattr(obj, 'execute') or 
                hasattr(obj, 'run') or
                name.endswith('Agent')):
                agent_classes.append(obj)
        
        return agent_classes
    
    def _register_discovered_agent(
        self,
        agent_name: str,
        agent_class: Type,
        agent_type: str,
        agent_dir: Path
    ) -> None:
        """
        Register a discovered agent.
        
        Args:
            agent_name: Name of the agent
            agent_class: Agent class
            agent_type: Type of agent
            agent_dir: Agent directory path
        """
        self._discovered_agents[agent_name] = agent_class
        
        # Create basic configuration from discovery
        config = self._create_config_from_discovery(
            agent_name,
            agent_class,
            agent_type,
            agent_dir
        )
        
        self._agents[agent_name] = config
        logger.debug(f"Registered agent: {agent_name}")
    
    def _create_config_from_discovery(
        self,
        agent_name: str,
        agent_class: Type,
        agent_type: str,
        agent_dir: Path
    ) -> AgentConfigModel:
        """
        Create agent configuration from discovery information.
        
        Args:
            agent_name: Name of the agent
            agent_class: Agent class
            agent_type: Type of agent
            agent_dir: Agent directory path
            
        Returns:
            Agent configuration model
        """
        # Extract basic information
        display_name = self._generate_display_name(agent_name)
        description = getattr(agent_class, '__doc__', f"{display_name} agent") or f"{display_name} agent"
        
        # Discover nodes
        nodes = self._discover_agent_nodes(agent_dir)
        
        # Get capabilities from class or directory structure
        capabilities = self._extract_capabilities(agent_class, agent_dir)
        
        # Create configuration
        config = AgentConfigModel(
            name=agent_name,
            display_name=display_name,
            description=description.strip(),
            agent_type=agent_type,
            nodes=nodes,
            capabilities=capabilities,
            icon=self._get_agent_icon(agent_name)
        )
        
        return config
    
    def _discover_agent_nodes(self, agent_dir: Path) -> Dict[str, NodeConfigModel]:
        """
        Discover nodes for an agent.
        
        Args:
            agent_dir: Agent directory path
            
        Returns:
            Dictionary of node configurations
        """
        nodes = {}
        
        # Look for nodes.py or nodes/ directory
        nodes_file = agent_dir / "nodes.py"
        nodes_dir = agent_dir / "nodes"
        
        if nodes_file.exists():
            nodes.update(self._discover_nodes_from_file(nodes_file))
        elif nodes_dir.exists():
            nodes.update(self._discover_nodes_from_directory(nodes_dir))
        
        # Look for graph.py for LangGraph nodes
        graph_file = agent_dir / "graph.py"
        if graph_file.exists():
            nodes.update(self._discover_nodes_from_graph(graph_file))
        
        return nodes
    
    def _discover_nodes_from_file(self, nodes_file: Path) -> Dict[str, NodeConfigModel]:
        """Discover nodes from a nodes.py file."""
        nodes = {}
        
        try:
            # Import the nodes module
            spec = importlib.util.spec_from_file_location("nodes", nodes_file)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            # Find node functions
            for name, obj in inspect.getmembers(module, inspect.isfunction):
                if not name.startswith('_'):
                    node_config = NodeConfigModel(
                        name=name,
                        display_name=self._generate_display_name(name),
                        description=getattr(obj, '__doc__', f"{name} node") or f"{name} node",
                        node_type="processor"  # Default type
                    )
                    nodes[name] = node_config
                    
        except Exception as e:
            logger.warning(f"Failed to discover nodes from {nodes_file}: {e}")
        
        return nodes
    
    def _discover_nodes_from_directory(self, nodes_dir: Path) -> Dict[str, NodeConfigModel]:
        """Discover nodes from a nodes/ directory."""
        nodes = {}
        
        for node_file in nodes_dir.glob("*.py"):
            if node_file.name != "__init__.py":
                node_name = node_file.stem
                node_config = NodeConfigModel(
                    name=node_name,
                    display_name=self._generate_display_name(node_name),
                    description=f"{node_name} node",
                    node_type="processor"
                )
                nodes[node_name] = node_config
        
        return nodes
    
    def _discover_nodes_from_graph(self, graph_file: Path) -> Dict[str, NodeConfigModel]:
        """Discover nodes from a graph.py file (LangGraph)."""
        nodes = {}
        
        try:
            # This would require more sophisticated parsing
            # For now, return empty dict
            pass
        except Exception as e:
            logger.warning(f"Failed to discover nodes from graph {graph_file}: {e}")
        
        return nodes
    
    def _generate_display_name(self, name: str) -> str:
        """Generate a human-readable display name from a technical name."""
        # Convert snake_case to Title Case
        return name.replace('_', ' ').title()
    
    def _extract_capabilities(self, agent_class: Type, agent_dir: Path) -> List[str]:
        """Extract capabilities from agent class or directory."""
        capabilities = []
        
        # Check for capabilities attribute
        if hasattr(agent_class, 'capabilities'):
            capabilities.extend(agent_class.capabilities)
        
        # Infer from directory structure
        if (agent_dir / "tools").exists():
            capabilities.append("tool_execution")
        if (agent_dir / "nodes").exists():
            capabilities.append("multi_step_workflow")
        
        return capabilities
    
    def _get_agent_icon(self, agent_name: str) -> str:
        """Get appropriate icon for agent."""
        icon_map = {
            'research': '🔍',
            'intake': '📝',
            'calendar': '📅',
            'task': '✅',
            'document': '📄',
            'supervisor': '👥',
            'deadline': '⏰',
            'matter_client': '⚖️'
        }
        
        for key, icon in icon_map.items():
            if key in agent_name.lower():
                return icon
        
        return '🤖'  # Default icon
    
    def _load_configurations(self) -> None:
        """Load configurations from files and database."""
        try:
            # Load from configuration files
            file_configs = self._config_loader.load_all_configs()
            
            # Merge with discovered configurations
            for agent_name, file_config in file_configs.items():
                if agent_name in self._agents:
                    # Merge configurations
                    self._agents[agent_name] = self._merge_configs(
                        self._agents[agent_name],
                        file_config
                    )
                else:
                    # Add new configuration
                    self._agents[agent_name] = file_config
            
            # Load from database (superadmin configurations)
            db_configs = self._config_loader.load_from_database()
            self._apply_database_configs(db_configs)
            
        except Exception as e:
            logger.error(f"Failed to load configurations: {e}")
    
    def _merge_configs(
        self, 
        base_config: AgentConfigModel, 
        override_config: AgentConfigModel
    ) -> AgentConfigModel:
        """Merge two agent configurations."""
        # Create a new config with base values
        merged_data = base_config.dict()
        
        # Override with new values (excluding None values)
        override_data = override_config.dict(exclude_none=True)
        
        # Deep merge
        for key, value in override_data.items():
            if key in merged_data and isinstance(merged_data[key], dict) and isinstance(value, dict):
                merged_data[key].update(value)
            else:
                merged_data[key] = value
        
        return AgentConfigModel(**merged_data)
    
    def _apply_database_configs(self, db_configs: Dict[str, Any]) -> None:
        """Apply database configurations (from superadmin)."""
        # This will integrate with the existing superadmin LLM selection system
        for agent_name, config_data in db_configs.items():
            if agent_name in self._agents:
                agent_config = self._agents[agent_name]
                
                # Apply model configurations
                if 'model_config' in config_data:
                    agent_config.model_config = ModelConfigModel(**config_data['model_config'])
                
                # Apply prompt configurations  
                if 'prompt_config' in config_data:
                    agent_config.prompt_config = PromptConfigModel(**config_data['prompt_config'])
                
                # Apply node-specific configurations
                if 'nodes' in config_data:
                    for node_name, node_data in config_data['nodes'].items():
                        if node_name in agent_config.nodes:
                            if 'model_config' in node_data:
                                agent_config.nodes[node_name].model_config = ModelConfigModel(**node_data['model_config'])
                            if 'prompt_config' in node_data:
                                agent_config.nodes[node_name].prompt_config = PromptConfigModel(**node_data['prompt_config'])
    
    # Public API methods
    
    def get_agent(self, agent_name: str) -> Optional[AgentConfigModel]:
        """Get agent configuration by name."""
        return self._agents.get(agent_name)
    
    def get_all_agents(self) -> Dict[str, AgentConfigModel]:
        """Get all registered agents."""
        return self._agents.copy()
    
    def register_agent(self, config: AgentConfigModel) -> None:
        """Register a new agent configuration."""
        if self._validator.validate_config(config):
            self._agents[config.name] = config
            logger.info(f"Registered agent: {config.name}")
        else:
            raise ValueError(f"Invalid agent configuration: {config.name}")
    
    def update_agent(self, agent_name: str, config: AgentConfigModel) -> None:
        """Update an existing agent configuration."""
        if agent_name not in self._agents:
            raise ValueError(f"Agent not found: {agent_name}")
        
        if self._validator.validate_config(config):
            self._agents[agent_name] = config
            logger.info(f"Updated agent: {agent_name}")
        else:
            raise ValueError(f"Invalid agent configuration: {agent_name}")
    
    def get_agents_by_type(self, agent_type: str) -> Dict[str, AgentConfigModel]:
        """Get agents by type."""
        return {
            name: config for name, config in self._agents.items()
            if config.agent_type == agent_type
        }
    
    def get_enabled_agents(self) -> Dict[str, AgentConfigModel]:
        """Get only enabled agents."""
        return {
            name: config for name, config in self._agents.items()
            if config.is_enabled
        }


# Global registry instance
_registry = None


def get_registry() -> AgentRegistry:
    """Get the global agent registry instance."""
    global _registry
    if _registry is None:
        _registry = AgentRegistry()
    return _registry


@lru_cache(maxsize=128)
def get_agent_config(agent_name: str, tenant_id: Optional[str] = None) -> Optional[AgentConfigModel]:
    """
    Get agent configuration with tenant-specific overrides.
    
    Args:
        agent_name: Name of the agent
        tenant_id: Tenant ID for tenant-specific configuration
        
    Returns:
        Agent configuration or None if not found
    """
    registry = get_registry()
    base_config = registry.get_agent(agent_name)
    
    if not base_config:
        return None
    
    # Apply tenant-specific overrides if needed
    if tenant_id:
        # This would load tenant-specific configurations
        # For now, return base configuration
        pass
    
    return base_config


def register_agent(config: AgentConfigModel) -> None:
    """Register a new agent configuration."""
    registry = get_registry()
    registry.register_agent(config)
