name: production
description: Production environment with optimized performance and monitoring

# Default model configuration for production
default_llm_config:
  provider: openai
  model_name: openai/gpt-4
  temperature: 0.1
  max_tokens: 4000

# Default prompt settings
default_prompt_settings:
  enable_debug_prompts: false
  include_reasoning: false
  verbose_responses: false

# Agent-specific overrides for production
agent_overrides:
  research_agent:
    llm_config:
      temperature: 0.2  # Balanced temperature for reliable research
      max_tokens: 3000
    metadata:
      enable_caching: true
      cache_ttl_hours: 24
  
  intake_agent:
    llm_config:
      temperature: 0.1  # Very low temperature for consistent intake
      max_tokens: 2000
    metadata:
      enable_strict_validation: true
      require_conflict_check: true
  
  supervisor_agent:
    llm_config:
      temperature: 0.05  # Extremely low temperature for consistent routing
      max_tokens: 1000
    metadata:
      enable_fallback_routing: true
      log_routing_failures: true

# Feature flags for production
feature_flags:
  enable_debug_mode: false
  enable_experimental_features: false
  enable_mock_responses: false
  enable_detailed_tracing: false
  enable_prompt_debugging: false
  enable_model_switching: false
  enable_test_data_generation: false
  enable_performance_optimization: true
  enable_security_hardening: true
  enable_rate_limiting: true

# Resource limits for production
max_tokens_per_request: 4000
max_requests_per_minute: 30
timeout_seconds: 30

# Monitoring and logging
enable_detailed_logging: false
enable_metrics: true
enable_tracing: false

# Production-specific settings
production_settings:
  auto_reload_configs: false
  enable_hot_swapping: false
  mock_external_apis: false
  use_production_database: true
  enable_cors: false
  debug_sql_queries: false
  enable_health_checks: true
  enable_circuit_breakers: true
  enable_retry_logic: true
  max_retry_attempts: 3
  retry_backoff_seconds: 2
