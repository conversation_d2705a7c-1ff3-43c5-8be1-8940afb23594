"""
Performance Tests for MCP Integration

This module contains performance and load tests for the MCP Rules Engine integration
to validate scalability, optimize batch sizes, and ensure the system performs well
under various load conditions.
"""

import pytest
import asyncio
import time
import statistics
from unittest.mock import patch, AsyncMock
from concurrent.futures import Thread<PERSON><PERSON><PERSON>xecutor
from datetime import datetime, timedelta

from backend.agents.insights.deadline.mcp_client import <PERSON>cp<PERSON><PERSON>, McpApiError
from backend.agents.insights.deadline.nodes import DeadlineInsightsNodes
from backend.agents.insights.deadline.state import (
    DeadlineInsightsState,
    AnalysisType,
    BatchProcessingConfig
)


class TestMcpClientPerformance:
    """Performance tests for MCP client."""
    
    @pytest.fixture
    def mock_mcp_client(self):
        """Create mock MCP client with realistic response times."""
        client = McpClient("https://test.example.com", "test-key")
        
        # Mock successful response with realistic delay
        async def mock_calculate_deadlines(*args, **kwargs):
            # Simulate realistic API response time (100-500ms)
            await asyncio.sleep(0.1 + (hash(str(args)) % 400) / 1000)
            return {
                "deadlines": [
                    {
                        "id": f"deadline_{hash(str(args)) % 1000}",
                        "name": "Test SOL Deadline",
                        "dueDate": "2024-12-01",
                        "priority": "high",
                        "category": "statute_of_limitations"
                    }
                ],
                "jurisdiction": args[0] if args else "TX",
                "triggerCode": args[1] if len(args) > 1 else "ACCIDENT_DATE"
            }
        
        client.calculate_deadlines = mock_calculate_deadlines
        return client
    
    @pytest.mark.asyncio
    async def test_concurrent_requests_performance(self, mock_mcp_client):
        """Test performance with concurrent requests."""
        num_requests = 20
        max_concurrent = 5
        
        async def make_request(request_id):
            start_time = time.time()
            try:
                async with mock_mcp_client:
                    result = await mock_mcp_client.calculate_deadlines(
                        f"TX_{request_id}",
                        "ACCIDENT_DATE",
                        "2022-01-15",
                        "personal_injury"
                    )
                end_time = time.time()
                return {
                    "request_id": request_id,
                    "duration": end_time - start_time,
                    "success": True,
                    "result": result
                }
            except Exception as e:
                end_time = time.time()
                return {
                    "request_id": request_id,
                    "duration": end_time - start_time,
                    "success": False,
                    "error": str(e)
                }
        
        # Create semaphore to limit concurrent requests
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def limited_request(request_id):
            async with semaphore:
                return await make_request(request_id)
        
        # Execute concurrent requests
        start_time = time.time()
        tasks = [limited_request(i) for i in range(num_requests)]
        results = await asyncio.gather(*tasks)
        total_time = time.time() - start_time
        
        # Analyze results
        successful_requests = [r for r in results if r["success"]]
        failed_requests = [r for r in results if not r["success"]]
        
        durations = [r["duration"] for r in successful_requests]
        
        # Performance assertions
        assert len(successful_requests) >= num_requests * 0.9  # At least 90% success rate
        assert total_time < 30  # Should complete within 30 seconds
        
        if durations:
            avg_duration = statistics.mean(durations)
            max_duration = max(durations)
            min_duration = min(durations)
            
            print(f"Performance Results:")
            print(f"  Total requests: {num_requests}")
            print(f"  Successful: {len(successful_requests)}")
            print(f"  Failed: {len(failed_requests)}")
            print(f"  Total time: {total_time:.2f}s")
            print(f"  Average request duration: {avg_duration:.3f}s")
            print(f"  Min duration: {min_duration:.3f}s")
            print(f"  Max duration: {max_duration:.3f}s")
            print(f"  Requests per second: {len(successful_requests) / total_time:.2f}")
            
            # Performance benchmarks
            assert avg_duration < 2.0  # Average request should be under 2 seconds
            assert max_duration < 5.0  # No request should take more than 5 seconds
    
    @pytest.mark.asyncio
    async def test_circuit_breaker_performance_impact(self, mock_mcp_client):
        """Test performance impact of circuit breaker under failure conditions."""
        # Mock client with intermittent failures
        failure_count = 0
        
        async def mock_calculate_with_failures(*args, **kwargs):
            nonlocal failure_count
            failure_count += 1
            
            # Fail first 3 requests to trigger circuit breaker
            if failure_count <= 3:
                raise McpApiError("Simulated failure", 500, "INTERNAL_ERROR")
            
            # Simulate recovery
            await asyncio.sleep(0.1)
            return {
                "deadlines": [{"id": "test", "name": "Test", "dueDate": "2024-01-01"}],
                "jurisdiction": "TX"
            }
        
        mock_mcp_client.calculate_deadlines = mock_calculate_with_failures
        
        # Test requests with circuit breaker behavior
        results = []
        for i in range(10):
            start_time = time.time()
            try:
                async with mock_mcp_client:
                    await mock_mcp_client.calculate_deadlines("TX", "ACCIDENT_DATE", "2022-01-15")
                success = True
                error = None
            except Exception as e:
                success = False
                error = str(e)
            
            duration = time.time() - start_time
            results.append({
                "request": i + 1,
                "success": success,
                "duration": duration,
                "error": error
            })
            
            # Small delay between requests
            await asyncio.sleep(0.05)
        
        # Analyze circuit breaker behavior
        failed_requests = [r for r in results if not r["success"]]
        successful_requests = [r for r in results if r["success"]]
        
        print(f"Circuit Breaker Performance:")
        for result in results:
            status = "✅" if result["success"] else "❌"
            print(f"  Request {result['request']}: {status} {result['duration']:.3f}s")
        
        # Should have some failures initially, then recovery
        assert len(failed_requests) >= 3  # Initial failures
        assert len(successful_requests) >= 3  # Recovery


class TestBatchProcessingPerformance:
    """Performance tests for batch processing."""
    
    @pytest.fixture
    def nodes(self):
        """Create DeadlineInsightsNodes for testing."""
        return DeadlineInsightsNodes()
    
    @pytest.fixture
    def large_matter_dataset(self):
        """Create large dataset for performance testing."""
        matters = []
        for i in range(100):
            matters.append({
                "id": f"matter-{i:03d}",
                "title": f"Performance Test Matter {i}",
                "practice_area": "personal_injury",
                "work_type": "litigation",
                "jurisdiction": "TX" if i % 2 == 0 else "CA",
                "filing_date": "2023-01-15",
                "metadata": {
                    "incident_date": "2022-12-01",
                    "case_type": "motor_vehicle_accident"
                }
            })
        return matters
    
    @pytest.mark.asyncio
    async def test_batch_size_optimization(self, nodes, large_matter_dataset):
        """Test different batch sizes to find optimal performance."""
        batch_sizes = [5, 10, 20, 50]
        results = {}
        
        # Mock MCP client with realistic performance
        mock_mcp_client = AsyncMock()
        mock_mcp_client.calculate_deadlines.return_value = {
            "deadlines": [{"id": "test", "name": "Test", "dueDate": "2024-01-01"}],
            "jurisdiction": "TX"
        }
        mock_mcp_client.__aenter__.return_value = mock_mcp_client
        mock_mcp_client.__aexit__.return_value = None
        
        for batch_size in batch_sizes:
            print(f"Testing batch size: {batch_size}")
            
            state = DeadlineInsightsState(
                tenant_id="performance-test",
                analysis_type=AnalysisType.BATCH_ANALYSIS,
                batch_config=BatchProcessingConfig(batch_size=batch_size)
            )
            
            # Use subset of data for faster testing
            test_matters = large_matter_dataset[:50]  # Use 50 matters
            
            with patch('backend.agents.insights.deadline.mcp_client.init_mcp_client', return_value=mock_mcp_client):
                with patch.object(nodes, '_get_repository'):
                    with patch.object(nodes, '_fetch_matters_for_sol_tracking', return_value=test_matters):
                        
                        start_time = time.time()
                        result_state = await nodes.process_batch_analysis(state)
                        duration = time.time() - start_time
                        
                        results[batch_size] = {
                            "duration": duration,
                            "success": result_state.status == "batch_processed",
                            "total_batches": result_state.total_batches,
                            "matters_processed": len(test_matters)
                        }
                        
                        print(f"  Duration: {duration:.2f}s")
                        print(f"  Batches: {result_state.total_batches}")
                        print(f"  Success: {result_state.status == 'batch_processed'}")
        
        # Analyze results
        print("\nBatch Size Performance Summary:")
        for batch_size, result in results.items():
            matters_per_second = result["matters_processed"] / result["duration"]
            print(f"  Batch size {batch_size:2d}: {result['duration']:6.2f}s, "
                  f"{matters_per_second:5.1f} matters/sec, "
                  f"{result['total_batches']:2d} batches")
        
        # Find optimal batch size (best matters per second)
        optimal_batch_size = max(results.keys(), 
                               key=lambda x: results[x]["matters_processed"] / results[x]["duration"])
        print(f"\nOptimal batch size: {optimal_batch_size}")
        
        # All batch sizes should complete successfully
        for batch_size, result in results.items():
            assert result["success"], f"Batch size {batch_size} failed"
    
    @pytest.mark.asyncio
    async def test_memory_usage_large_dataset(self, nodes, large_matter_dataset):
        """Test memory usage with large datasets."""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        state = DeadlineInsightsState(
            tenant_id="memory-test",
            analysis_type=AnalysisType.BATCH_ANALYSIS,
            batch_config=BatchProcessingConfig(batch_size=10)
        )
        
        # Mock MCP client
        mock_mcp_client = AsyncMock()
        mock_mcp_client.calculate_deadlines.return_value = {
            "deadlines": [{"id": "test", "name": "Test", "dueDate": "2024-01-01"}],
            "jurisdiction": "TX"
        }
        mock_mcp_client.__aenter__.return_value = mock_mcp_client
        mock_mcp_client.__aexit__.return_value = None
        
        with patch('backend.agents.insights.deadline.mcp_client.init_mcp_client', return_value=mock_mcp_client):
            with patch.object(nodes, '_get_repository'):
                with patch.object(nodes, '_fetch_matters_for_sol_tracking', return_value=large_matter_dataset):
                    
                    result_state = await nodes.process_batch_analysis(state)
                    
                    final_memory = process.memory_info().rss / 1024 / 1024  # MB
                    memory_increase = final_memory - initial_memory
                    
                    print(f"Memory Usage:")
                    print(f"  Initial: {initial_memory:.1f} MB")
                    print(f"  Final: {final_memory:.1f} MB")
                    print(f"  Increase: {memory_increase:.1f} MB")
                    print(f"  Per matter: {memory_increase / len(large_matter_dataset):.3f} MB")
                    
                    # Memory usage should be reasonable
                    assert memory_increase < 100, f"Memory increase too high: {memory_increase:.1f} MB"
                    assert result_state.status == "batch_processed"
    
    @pytest.mark.asyncio
    async def test_error_recovery_performance(self, nodes):
        """Test performance impact of error recovery mechanisms."""
        # Create test matters
        test_matters = [
            {
                "id": f"error-test-{i}",
                "title": f"Error Test Matter {i}",
                "practice_area": "personal_injury",
                "jurisdiction": "TX",
                "metadata": {"incident_date": "2022-01-01", "case_type": "motor_vehicle_accident"}
            }
            for i in range(20)
        ]
        
        # Mock MCP client with intermittent failures
        call_count = 0
        
        async def mock_calculate_with_errors(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            
            # Fail every 3rd request to test retry logic
            if call_count % 3 == 0:
                raise McpApiError("Intermittent failure", 503, "SERVICE_UNAVAILABLE")
            
            await asyncio.sleep(0.1)  # Simulate API delay
            return {
                "deadlines": [{"id": "test", "name": "Test", "dueDate": "2024-01-01"}],
                "jurisdiction": "TX"
            }
        
        mock_mcp_client = AsyncMock()
        mock_mcp_client.calculate_deadlines = mock_calculate_with_errors
        mock_mcp_client.__aenter__.return_value = mock_mcp_client
        mock_mcp_client.__aexit__.return_value = None
        
        state = DeadlineInsightsState(
            tenant_id="error-recovery-test",
            analysis_type=AnalysisType.BATCH_ANALYSIS,
            batch_config=BatchProcessingConfig(batch_size=5)
        )
        
        with patch('backend.agents.insights.deadline.mcp_client.init_mcp_client', return_value=mock_mcp_client):
            with patch.object(nodes, '_get_repository'):
                with patch.object(nodes, '_fetch_matters_for_sol_tracking', return_value=test_matters):
                    
                    start_time = time.time()
                    result_state = await nodes.process_batch_analysis(state)
                    duration = time.time() - start_time
                    
                    print(f"Error Recovery Performance:")
                    print(f"  Duration: {duration:.2f}s")
                    print(f"  Status: {result_state.status}")
                    print(f"  Total API calls: {call_count}")
                    
                    # Should complete despite errors (with retries)
                    assert result_state.status == "batch_processed"
                    assert call_count > len(test_matters)  # More calls due to retries
                    
                    # Check batch metadata for error information
                    if hasattr(result_state, 'metadata') and 'batch_results' in result_state.metadata:
                        batch_results = result_state.metadata['batch_results']
                        print(f"  Errors: {batch_results.get('error_count', 0)}")
                        print(f"  Processed: {batch_results.get('total_matters_processed', 0)}")


if __name__ == "__main__":
    # Allow running performance tests directly
    pytest.main([__file__, "-v", "-s", "--tb=short"])
