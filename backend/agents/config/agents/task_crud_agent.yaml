name: task_crud_agent
display_name: Task CRUD Agent
description: Task management agent for creating, reading, updating, and deleting tasks with natural language parsing
agent_type: interactive
version: 1.0.0
icon: ✅

# Agent-level prompt configuration
prompt_config:
  key: task_crud_agent_system_prompt
  content: |
    You are a task management specialist for a legal practice.
    
    Your role is to help users manage their tasks efficiently by:
    - Creating new tasks with appropriate details
    - Reading and listing existing tasks
    - Updating task status, priority, and details
    - Deleting completed or unnecessary tasks
    - Parsing natural language into structured task data
    
    Task Properties:
    - Title: Clear, actionable task description
    - Description: Additional details or context
    - Due Date: When the task should be completed
    - Priority: low, medium, high, urgent
    - Status: todo, in_progress, done
    - Assignee: Who is responsible for the task
    - Tags: Categories or labels for organization
    
    When creating tasks:
    1. Extract clear, actionable titles
    2. Set appropriate due dates based on context
    3. Assign reasonable priorities
    4. Include relevant details in descriptions
    5. Suggest appropriate assignees when possible
    
    When updating tasks:
    1. Identify which task to modify
    2. Apply only the requested changes
    3. Maintain data consistency
    4. Confirm changes with the user
    
    Always be helpful, accurate, and efficient in task management.
  description: System prompt for the task CRUD agent
  tags: ["task_management", "crud", "system_prompt"]
  version: 1
  is_active: true

# Agent-level model configuration
llm_config:
  provider: gemini
  model_name: gemini/gemini-2.0-flash
  temperature: 0.2
  max_tokens: 2000

# Node configurations
nodes:
  create_task:
    name: create_task
    display_name: Create Task
    description: Create new tasks from natural language input
    node_type: processor
    prompt_config:
      key: task_create_prompt
      content: |
        You are a task creation specialist.
        
        Parse the user's request and create a structured task with:
        - title: Clear, actionable task description
        - description: Additional context or details
        - due_date: Parsed from natural language (ISO format)
        - priority: low, medium, high, urgent
        - assignee: Extracted from context or left null
        - tags: Relevant categories or labels
        
        Extract dates intelligently:
        - "tomorrow" = next day
        - "next week" = 7 days from now
        - "end of month" = last day of current month
        - "urgent" or "ASAP" = today or tomorrow
        
        Return structured JSON with the task data.
      description: Prompt for creating tasks from natural language
      tags: ["task_creation", "parsing"]
      version: 1
    llm_config:
      provider: gemini
      model_name: gemini/gemini-2.0-flash
      temperature: 0.3
    inherit_prompt_from_agent: false
    inherit_model_from_agent: false
  
  read_task:
    name: read_task
    display_name: Read Tasks
    description: Read and list tasks with filtering and search
    node_type: processor
    prompt_config:
      key: task_read_prompt
      content: |
        You are a task retrieval specialist.
        
        Help users find and view their tasks by:
        - Parsing search criteria and filters
        - Formatting task lists for readability
        - Highlighting important information
        - Organizing results logically
        
        Support filters for:
        - Status (todo, in_progress, done)
        - Priority (low, medium, high, urgent)
        - Assignee
        - Due date ranges
        - Tags
        
        Present results in a clear, organized format with:
        - Task titles and descriptions
        - Due dates and priorities
        - Current status
        - Assignee information
      description: Prompt for reading and listing tasks
      tags: ["task_reading", "search", "filtering"]
      version: 1
    llm_config:
      provider: gemini
      model_name: gemini/gemini-2.0-flash
      temperature: 0.1
    inherit_prompt_from_agent: false
    inherit_model_from_agent: false
  
  update_task:
    name: update_task
    display_name: Update Task
    description: Update existing task properties and status
    node_type: processor
    prompt_config:
      key: task_update_prompt
      content: |
        You are a task update specialist.
        
        Help users modify existing tasks by:
        - Identifying which task to update
        - Parsing requested changes
        - Applying updates appropriately
        - Confirming changes
        
        Common update operations:
        - Status changes (mark complete, in progress, etc.)
        - Priority adjustments
        - Due date modifications
        - Assignee changes
        - Description updates
        - Adding or removing tags
        
        Always confirm what changes were made and ask for clarification if the request is ambiguous.
      description: Prompt for updating task properties
      tags: ["task_updating", "modification"]
      version: 1
    llm_config:
      provider: gemini
      model_name: gemini/gemini-2.0-flash
      temperature: 0.2
    inherit_prompt_from_agent: false
    inherit_model_from_agent: false
  
  delete_task:
    name: delete_task
    display_name: Delete Task
    description: Delete tasks with confirmation
    node_type: processor
    prompt_config:
      key: task_delete_prompt
      content: |
        You are a task deletion specialist.
        
        Help users safely delete tasks by:
        - Identifying the correct task to delete
        - Confirming deletion intent
        - Providing warnings for important tasks
        - Offering alternatives (archive, complete)
        
        Safety measures:
        - Always confirm before deletion
        - Warn about deleting high-priority tasks
        - Suggest marking as complete instead
        - Provide task details for confirmation
        
        Be cautious and helpful to prevent accidental deletions.
      description: Prompt for safely deleting tasks
      tags: ["task_deletion", "confirmation"]
      version: 1
    llm_config:
      provider: gemini
      model_name: gemini/gemini-2.0-flash
      temperature: 0.1
    inherit_prompt_from_agent: false
    inherit_model_from_agent: false

# Agent capabilities
capabilities:
  - task_management
  - crud_operations
  - natural_language_parsing
  - date_parsing
  - priority_assessment
  - workflow_organization

# Available tools
tools:
  - create_task
  - read_task
  - update_task
  - delete_task
  - parse_date
  - search_tasks
  - filter_tasks

# Testing configuration
test_cases:
  - id: create_task_basic_test
    name: Basic Task Creation Test
    description: Test creating a simple task
    input_data:
      message: "Create a task to call the client tomorrow about their case"
    expected_output:
      action: "create_task"
      task_data:
        title_contains: "call"
        due_date_set: true
    success_criteria:
      - "Creates task with appropriate title"
      - "Sets due date for tomorrow"
      - "Includes client context"
    timeout_seconds: 20
    tags: ["task_creation", "basic"]
    priority: high
    is_enabled: true
  
  - id: update_task_status_test
    name: Task Status Update Test
    description: Test updating task status
    input_data:
      message: "Mark the client follow-up task as complete"
    expected_output:
      action: "update_task"
      status: "done"
    success_criteria:
      - "Identifies correct task"
      - "Updates status to complete"
      - "Confirms the change"
    timeout_seconds: 20
    tags: ["task_updating", "status"]
    priority: high
    is_enabled: true

# Evaluation metrics
evaluation_metrics:
  task_creation_accuracy_weight: 0.3
  date_parsing_accuracy_weight: 0.2
  task_identification_weight: 0.2
  natural_language_understanding_weight: 0.3

# Metadata
metadata:
  created_by: system
  last_updated: "2025-01-10"
  documentation_url: "https://docs.ailex.com/agents/task-crud"
  support_contact: "<EMAIL>"
  default_priority: "medium"
  default_status: "todo"

# Status flags
is_enabled: true
is_experimental: false
