name: development
description: Development environment with verbose logging and relaxed limits

# Default model configuration for development
default_llm_config:
  provider: openai
  model_name: openai/gpt-3.5-turbo
  temperature: 0.3
  max_tokens: 2000

# Default prompt settings
default_prompt_settings:
  enable_debug_prompts: true
  include_reasoning: true
  verbose_responses: true

# Agent-specific overrides for development
agent_overrides:
  research_agent:
    llm_config:
      temperature: 0.4  # Higher temperature for more creative research
    metadata:
      enable_mock_search: true
      cache_results: false
  
  intake_agent:
    llm_config:
      temperature: 0.2  # Lower temperature for consistent intake
    metadata:
      enable_test_mode: true
      skip_conflict_check: true
  
  supervisor_agent:
    llm_config:
      temperature: 0.1  # Very low temperature for consistent routing
    metadata:
      enable_debug_routing: true
      log_routing_decisions: true

# Feature flags for development
feature_flags:
  enable_debug_mode: true
  enable_experimental_features: true
  enable_mock_responses: true
  enable_detailed_tracing: true
  enable_prompt_debugging: true
  enable_model_switching: true
  enable_test_data_generation: true

# Resource limits for development
max_tokens_per_request: 2000
max_requests_per_minute: 100
timeout_seconds: 60

# Monitoring and logging
enable_detailed_logging: true
enable_metrics: true
enable_tracing: true

# Development-specific settings
development_settings:
  auto_reload_configs: true
  enable_hot_swapping: true
  mock_external_apis: true
  use_test_database: true
  enable_cors: true
  debug_sql_queries: true
