name: research_agent
display_name: Research Agent
description: Legal research agent for finding relevant laws, statutes, and case precedents
agent_type: interactive
version: 1.0.0
icon: 🔍

# Agent-level prompt configuration
prompt_config:
  key: research_agent_system_prompt
  content: |
    You are a legal research assistant specialized in finding relevant laws, statutes, and case precedents.
    
    Your capabilities include:
    - Searching legal databases and public law resources
    - Finding relevant case law and precedents
    - Analyzing legal documents and extracting key information
    - Providing citations in proper legal format
    - Explaining complex legal concepts in accessible language
    
    When conducting research:
    1. Always provide accurate citations
    2. Distinguish between primary and secondary sources
    3. Note the jurisdiction and date of laws/cases
    4. Explain the relevance to the user's query
    5. Suggest related areas of law to explore
    
    Be thorough, accurate, and helpful in your research assistance.
  description: System prompt for the research agent
  tags: ["research", "legal", "system_prompt"]
  version: 1
  is_active: true

# Agent-level model configuration
llm_config:
  provider: openai
  model_name: openai/gpt-4
  temperature: 0.3
  max_tokens: 3000

# Node configurations
nodes:
  query_gen:
    name: query_gen
    display_name: Query Generation
    description: Generate optimized search queries from user input
    node_type: generator
    prompt_config:
      key: research_query_generation_prompt
      content: |
        You are a query generation specialist for legal research.
        
        Your task is to transform user questions into optimized search queries for legal databases.
        
        Generate 2-3 search queries that:
        1. Use proper legal terminology
        2. Include relevant synonyms and variations
        3. Consider different aspects of the legal question
        4. Are optimized for vector search and keyword search
        
        Format your response as a JSON array of query strings.
      description: Prompt for generating optimized search queries
      tags: ["query_generation", "search"]
      version: 1
    llm_config:
      provider: openai
      model_name: openai/gpt-4
      temperature: 0.4
    inherit_prompt_from_agent: false
    inherit_model_from_agent: false
  
  rerank:
    name: rerank
    display_name: Result Reranking
    description: Rerank search results by relevance to user query
    node_type: processor
    prompt_config:
      key: research_rerank_prompt
      content: |
        You are a legal research result ranking specialist.
        
        Your task is to rerank search results based on their relevance to the user's query.
        
        Consider:
        1. Direct relevance to the legal question
        2. Jurisdiction applicability
        3. Recency and current validity
        4. Authority level (primary vs secondary sources)
        5. Specificity to the user's situation
        
        Provide a ranked list with brief explanations for the top results.
      description: Prompt for reranking search results
      tags: ["reranking", "relevance"]
      version: 1
    llm_config:
      provider: openai
      model_name: openai/gpt-4
      temperature: 0.2
    inherit_prompt_from_agent: false
    inherit_model_from_agent: false
  
  exit_guard:
    name: exit_guard
    display_name: Exit Guard
    description: Determine if research is complete or needs continuation
    node_type: validator
    prompt_config:
      key: research_exit_guard_prompt
      content: |
        You are a research completion validator.
        
        Determine if the current research results adequately answer the user's question.
        
        Consider:
        1. Completeness of the answer
        2. Quality of sources found
        3. Coverage of different aspects of the question
        4. Need for additional research
        
        Respond with "COMPLETE" if research is sufficient, or "CONTINUE" with specific areas needing more research.
      description: Prompt for determining research completion
      tags: ["validation", "completion"]
      version: 1
    llm_config:
      provider: openai
      model_name: openai/gpt-4o-mini
      temperature: 0.0
    inherit_prompt_from_agent: false
    inherit_model_from_agent: false

# Agent capabilities
capabilities:
  - legal_research
  - document_search
  - citation_formatting
  - case_law_analysis
  - statute_interpretation
  - multi_step_workflow
  - tool_execution

# Available tools
tools:
  - search_documents
  - enqueue_async_job
  - format_citations
  - extract_legal_entities

# Testing configuration
test_cases:
  - id: basic_research_test
    name: Basic Legal Research Test
    description: Test basic research functionality
    input_data:
      message: "What is the statute of limitations for personal injury cases in Texas?"
    expected_output:
      type: "research_response"
      contains: ["statute of limitations", "Texas", "personal injury"]
    success_criteria:
      - "Agent provides relevant information"
      - "Response includes proper citations"
      - "Mentions Texas-specific law"
    timeout_seconds: 30
    tags: ["basic", "research", "statute_of_limitations"]
    priority: high
    is_enabled: true

# Evaluation metrics
evaluation_metrics:
  accuracy_weight: 0.4
  relevance_weight: 0.3
  citation_quality_weight: 0.2
  response_time_weight: 0.1

# Metadata
metadata:
  created_by: system
  last_updated: "2025-01-10"
  documentation_url: "https://docs.ailex.com/agents/research"
  support_contact: "<EMAIL>"

# Status flags
is_enabled: true
is_experimental: false
