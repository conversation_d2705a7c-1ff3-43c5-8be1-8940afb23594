name: supervisor_agent
display_name: Supervisor Agent
description: Main routing and coordination agent for the AiLex platform that dispatches requests to specialized agents
agent_type: insights
version: 1.0.0
icon: 👥

# Agent-level prompt configuration
prompt_config:
  key: supervisor_agent_system_prompt
  content: |
    You are the AiLex supervisor. Your job is to decide which specialized agent
    should handle the user's request and what arguments to pass.

    Available agents:
    - intakeAgent: For new client intake and case setup
    - researchAgent: For legal research and finding relevant laws
    - taskCrudAgent: For task management operations
    - calendarCrudAgent: For calendar and scheduling operations
    - documentDraftAgent: For document drafting and generation
    - insightSwarmAgent: For complex analysis and insights

    When you decide, respond **only** with valid JSON:

    {
      "agent": "<one of: intakeAgent | researchAgent | taskCrudAgent | calendarCrudAgent | documentDraftAgent | insightSwarmAgent>",
      "args":  { ... },         # optional
      "confidence": 0.95        # required, between 0.0 and 1.0
    }

    Classification guidelines:
    - intakeAgent: New client inquiries, case intake, initial consultations
    - researchAgent: Legal research, case law, statutes, precedents
    - taskCrudAgent: Creating, updating, managing tasks and to-dos
    - calendarCrudAgent: Scheduling, appointments, calendar management
    - documentDraftAgent: Creating legal documents, contracts, letters
    - insightSwarmAgent: Complex analysis, strategy, multi-step reasoning

    Always provide high confidence (>0.8) for clear requests.
    Use lower confidence (0.6-0.8) when the request could fit multiple agents.
    Never go below 0.6 confidence.
  description: System prompt for the supervisor agent routing decisions
  tags: ["supervisor", "routing", "system_prompt"]
  version: 1
  is_active: true

# Agent-level model configuration
llm_config:
  provider: openai
  model_name: openai/gpt-4o
  temperature: 0.2
  max_tokens: 1000

# No nodes for supervisor agent - it's a single-step router
nodes: {}

# Agent capabilities
capabilities:
  - intent_routing
  - task_coordination
  - agent_supervision
  - workflow_management
  - request_classification
  - confidence_assessment

# Available tools
tools:
  - route_to_agent
  - log_routing_decision
  - fallback_handler

# Testing configuration
test_cases:
  - id: routing_research_test
    name: Research Request Routing Test
    description: Test routing of research requests to research agent
    input_data:
      message: "I need to research the statute of limitations for personal injury cases in Texas"
    expected_output:
      agent: "researchAgent"
      confidence_min: 0.8
    success_criteria:
      - "Routes to researchAgent"
      - "Confidence is above 0.8"
      - "Response is valid JSON"
    timeout_seconds: 10
    tags: ["routing", "research", "classification"]
    priority: high
    is_enabled: true
  
  - id: routing_intake_test
    name: Intake Request Routing Test
    description: Test routing of intake requests to intake agent
    input_data:
      message: "I was in a car accident and need legal help"
    expected_output:
      agent: "intakeAgent"
      confidence_min: 0.8
    success_criteria:
      - "Routes to intakeAgent"
      - "Confidence is above 0.8"
      - "Response is valid JSON"
    timeout_seconds: 10
    tags: ["routing", "intake", "classification"]
    priority: high
    is_enabled: true
  
  - id: routing_task_test
    name: Task Management Routing Test
    description: Test routing of task management requests
    input_data:
      message: "Create a task to follow up with the client next week"
    expected_output:
      agent: "taskCrudAgent"
      confidence_min: 0.8
    success_criteria:
      - "Routes to taskCrudAgent"
      - "Confidence is above 0.8"
      - "Response is valid JSON"
    timeout_seconds: 10
    tags: ["routing", "tasks", "classification"]
    priority: high
    is_enabled: true

# Evaluation metrics
evaluation_metrics:
  routing_accuracy_weight: 0.5
  confidence_calibration_weight: 0.3
  response_time_weight: 0.2

# Metadata
metadata:
  created_by: system
  last_updated: "2025-01-10"
  documentation_url: "https://docs.ailex.com/agents/supervisor"
  support_contact: "<EMAIL>"
  routing_confidence_threshold: 0.6
  fallback_agent: "insightSwarmAgent"

# Status flags
is_enabled: true
is_experimental: false
