"""
Tests for MCP Rules Engine Integration in Deadline Insights Agent

This module contains comprehensive tests for the MCP integration including:
- MCP client functionality
- SOL tracking logic
- Batch processing with MCP
- Error handling and retry logic
- Circuit breaker functionality
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime, timezone, timedelta

from backend.agents.insights.deadline.mcp_client import (
    Mcp<PERSON>lient, 
    McpApiError, 
    init_mcp_client, 
    get_mcp_api_key,
    CircuitBreakerState
)
from backend.agents.insights.deadline.nodes import DeadlineInsightsNodes
from backend.agents.insights.deadline.state import (
    DeadlineInsightsState,
    AnalysisType,
    BatchProcessingConfig
)


class TestMcpClient:
    """Test MCP client functionality."""
    
    def test_client_initialization(self):
        """Test MCP client initialization."""
        client = McpClient(
            base_url="https://test.example.com",
            api_key="test-key",
            timeout=30,
            max_retries=3
        )
        
        assert client.base_url == "https://test.example.com"
        assert client.api_key == "test-key"
        assert client.timeout == 30
        assert client.max_retries == 3
        assert client.circuit_breaker_state == CircuitBreakerState.CLOSED
    
    def test_circuit_breaker_state(self):
        """Test circuit breaker state tracking."""
        client = McpClient("https://test.example.com", "test-key")
        
        state = client.get_circuit_breaker_state()
        assert state["state"] == "closed"
        assert state["consecutiveFailures"] == 0
        assert state["lastFailureTime"] == 0
    
    @pytest.mark.asyncio
    async def test_calculate_deadlines_success(self):
        """Test successful deadline calculation."""
        client = McpClient("https://test.example.com", "test-key")
        
        mock_response = {
            "deadlines": [
                {
                    "id": "deadline_1",
                    "name": "SOL Deadline",
                    "dueDate": "2024-01-15",
                    "priority": "high",
                    "category": "statute_of_limitations",
                    "description": "Personal injury statute of limitations",
                    "legalBasis": "Texas Civil Practice and Remedies Code",
                    "consequences": "Case may be time-barred"
                }
            ],
            "jurisdiction": "TX",
            "triggerCode": "ACCIDENT_DATE",
            "startDate": "2022-01-15",
            "calculatedAt": "2023-01-15T10:00:00Z"
        }
        
        with patch.object(client, '_make_request', new_callable=AsyncMock) as mock_request:
            mock_request.return_value = mock_response
            
            result = await client.calculate_deadlines(
                jurisdiction="TX",
                trigger_code="ACCIDENT_DATE",
                start_date="2022-01-15",
                practice_area="personal_injury"
            )
            
            assert result == mock_response
            mock_request.assert_called_once_with(
                "/api/v1/deadlines/calculate",
                method="POST",
                data={
                    "jurisdiction": "TX",
                    "triggerCode": "ACCIDENT_DATE",
                    "startDate": "2022-01-15",
                    "practiceArea": "personal_injury"
                }
            )
    
    @pytest.mark.asyncio
    async def test_calculate_deadlines_error(self):
        """Test deadline calculation with API error."""
        client = McpClient("https://test.example.com", "test-key")
        
        with patch.object(client, '_make_request', new_callable=AsyncMock) as mock_request:
            mock_request.side_effect = McpApiError("API Error", 500, "INTERNAL_ERROR")
            
            with pytest.raises(McpApiError) as exc_info:
                await client.calculate_deadlines("TX", "ACCIDENT_DATE", "2022-01-15")
            
            assert exc_info.value.status_code == 500
            assert exc_info.value.error_code == "INTERNAL_ERROR"
    
    @pytest.mark.asyncio
    async def test_health_check(self):
        """Test health check functionality."""
        client = McpClient("https://test.example.com", "test-key")
        
        mock_response = {"status": "healthy", "timestamp": "2023-01-15T10:00:00Z"}
        
        with patch.object(client, '_make_request', new_callable=AsyncMock) as mock_request:
            mock_request.return_value = mock_response
            
            result = await client.health_check()
            
            assert result == mock_response
            mock_request.assert_called_once_with("/api/v1/health", method="GET")


class TestMcpApiKeyManagement:
    """Test MCP API key management."""
    
    @pytest.mark.asyncio
    async def test_get_mcp_api_key_success(self):
        """Test successful API key retrieval."""
        tenant_id = "test-tenant"
        expected_key = "test-api-key-123"
        
        mock_client = Mock()
        mock_response = Mock()
        mock_response.payload.data.decode.return_value = expected_key
        mock_client.access_secret_version.return_value = mock_response
        
        with patch('backend.agents.insights.deadline.mcp_client.secretmanager.SecretManagerServiceClient', return_value=mock_client):
            result = await get_mcp_api_key(tenant_id)
            
            assert result == expected_key
            mock_client.access_secret_version.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_mcp_api_key_caching(self):
        """Test API key caching functionality."""
        tenant_id = "test-tenant"
        expected_key = "test-api-key-123"
        
        # Clear cache
        from backend.agents.insights.deadline.mcp_client import api_key_cache
        api_key_cache.clear()
        
        mock_client = Mock()
        mock_response = Mock()
        mock_response.payload.data.decode.return_value = expected_key
        mock_client.access_secret_version.return_value = mock_response
        
        with patch('backend.agents.insights.deadline.mcp_client.secretmanager.SecretManagerServiceClient', return_value=mock_client):
            # First call should hit the API
            result1 = await get_mcp_api_key(tenant_id)
            assert result1 == expected_key
            
            # Second call should use cache
            result2 = await get_mcp_api_key(tenant_id)
            assert result2 == expected_key
            
            # Should only call the API once
            assert mock_client.access_secret_version.call_count == 1
    
    @pytest.mark.asyncio
    async def test_init_mcp_client(self):
        """Test MCP client initialization with Secret Manager."""
        tenant_id = "test-tenant"
        expected_key = "test-api-key-123"
        
        with patch('backend.agents.insights.deadline.mcp_client.get_mcp_api_key', new_callable=AsyncMock) as mock_get_key:
            mock_get_key.return_value = expected_key
            
            client = await init_mcp_client(tenant_id)
            
            assert isinstance(client, McpClient)
            assert client.api_key == expected_key
            mock_get_key.assert_called_once_with(tenant_id)


class TestSolTrackingLogic:
    """Test SOL tracking logic in deadline insights nodes."""
    
    @pytest.fixture
    def nodes(self):
        """Create DeadlineInsightsNodes instance for testing."""
        return DeadlineInsightsNodes()
    
    @pytest.fixture
    def sample_state(self):
        """Create sample state for testing."""
        return DeadlineInsightsState(
            tenant_id="test-tenant",
            matter_ids=["matter-1", "matter-2"],
            analysis_type=AnalysisType.SOL_TRACKING
        )
    
    @pytest.fixture
    def sample_matter(self):
        """Create sample matter data for testing."""
        return {
            "id": "matter-1",
            "title": "Personal Injury Case",
            "practice_area": "personal_injury",
            "work_type": "litigation",
            "jurisdiction": "TX",
            "filing_date": "2023-01-15",
            "opened_date": "2023-01-10",
            "metadata": {
                "incident_date": "2022-12-01",
                "case_type": "motor_vehicle_accident"
            }
        }
    
    def test_extract_sol_parameters(self, nodes, sample_matter):
        """Test SOL parameter extraction from matter data."""
        params = nodes._extract_sol_parameters(sample_matter)
        
        assert params is not None
        assert params["jurisdiction"] == "TX"
        assert params["trigger_code"] == "ACCIDENT_DATE"
        assert params["start_date"] == "2022-12-01"
        assert params["practice_area"] == "personal_injury"
    
    def test_extract_sol_parameters_missing_data(self, nodes):
        """Test SOL parameter extraction with missing data."""
        incomplete_matter = {
            "id": "matter-1",
            "title": "Incomplete Matter"
        }
        
        params = nodes._extract_sol_parameters(incomplete_matter)
        
        # Should still return parameters with defaults
        assert params is not None
        assert params["jurisdiction"] == "TX"  # Default
        assert params["practice_area"] == "personal_injury"  # Default
    
    def test_determine_trigger_code(self, nodes, sample_matter):
        """Test trigger code determination logic."""
        # Test motor vehicle accident
        trigger = nodes._determine_trigger_code(sample_matter)
        assert trigger == "ACCIDENT_DATE"
        
        # Test medical malpractice
        sample_matter["metadata"]["case_type"] = "medical_malpractice"
        trigger = nodes._determine_trigger_code(sample_matter)
        assert trigger == "DISCOVERY_DATE"
        
        # Test default for unknown case type
        sample_matter["metadata"]["case_type"] = "unknown_type"
        trigger = nodes._determine_trigger_code(sample_matter)
        assert trigger == "ACCIDENT_DATE"  # Default for personal injury
    
    def test_determine_sol_start_date(self, nodes, sample_matter):
        """Test SOL start date determination."""
        start_date = nodes._determine_sol_start_date(sample_matter)
        assert start_date == "2022-12-01"  # From incident_date
        
        # Test fallback to filing_date
        del sample_matter["metadata"]["incident_date"]
        start_date = nodes._determine_sol_start_date(sample_matter)
        assert start_date == "2023-01-15"  # From filing_date
    
    def test_process_mcp_response(self, nodes, sample_matter):
        """Test MCP response processing."""
        mcp_response = {
            "deadlines": [
                {
                    "id": "deadline_1",
                    "name": "SOL Deadline",
                    "dueDate": "2024-12-01",
                    "priority": "high",
                    "category": "statute_of_limitations",
                    "description": "Personal injury SOL",
                    "legalBasis": "Texas Civil Practice Code",
                    "consequences": "Case time-barred"
                }
            ],
            "jurisdiction": "TX",
            "triggerCode": "ACCIDENT_DATE",
            "calculatedAt": "2023-01-15T10:00:00Z"
        }
        
        deadlines = nodes._process_mcp_response(sample_matter, mcp_response)
        
        assert len(deadlines) == 1
        deadline = deadlines[0]
        assert deadline["matter_id"] == "matter-1"
        assert deadline["title"] == "SOL Deadline"
        assert deadline["due_date"] == "2024-12-01"
        assert deadline["priority"] == "high"
        assert deadline["source"] == "mcp_rules_engine"
        assert deadline["metadata"]["matter_title"] == "Personal Injury Case"
    
    def test_is_high_risk_deadline(self, nodes):
        """Test high-risk deadline detection."""
        # High-risk deadline (within 90 days)
        future_date = (datetime.now() + timedelta(days=30)).isoformat()
        high_risk_deadline = {"due_date": future_date}
        assert nodes._is_high_risk_deadline(high_risk_deadline) is True
        
        # Low-risk deadline (more than 90 days)
        far_future_date = (datetime.now() + timedelta(days=120)).isoformat()
        low_risk_deadline = {"due_date": far_future_date}
        assert nodes._is_high_risk_deadline(low_risk_deadline) is False
        
        # Past deadline
        past_date = (datetime.now() - timedelta(days=30)).isoformat()
        past_deadline = {"due_date": past_date}
        assert nodes._is_high_risk_deadline(past_deadline) is False
    
    def test_generate_sol_recommendations(self, nodes):
        """Test SOL recommendation generation."""
        sol_results = [
            {
                "id": "deadline_1",
                "jurisdiction": "TX",
                "metadata": {"practice_area": "personal_injury"}
            },
            {
                "id": "deadline_2", 
                "jurisdiction": "CA",
                "metadata": {"practice_area": "medical_malpractice"}
            }
        ]
        high_risk_deadlines = ["deadline_1"]
        
        recommendations = nodes._generate_sol_recommendations(sol_results, high_risk_deadlines)
        
        assert len(recommendations) > 0
        assert any("URGENT" in rec for rec in recommendations)
        assert any("Personal injury cases" in rec for rec in recommendations)
        assert any("Multi-jurisdiction" in rec for rec in recommendations)


class TestBatchProcessingIntegration:
    """Test batch processing with MCP integration."""

    @pytest.fixture
    def nodes(self):
        """Create DeadlineInsightsNodes instance for testing."""
        return DeadlineInsightsNodes()

    @pytest.fixture
    def batch_state(self):
        """Create batch processing state for testing."""
        return DeadlineInsightsState(
            tenant_id="test-tenant",
            matter_ids=["matter-1", "matter-2", "matter-3"],
            analysis_type=AnalysisType.BATCH_ANALYSIS,
            batch_config=BatchProcessingConfig(batch_size=2)
        )

    @pytest.fixture
    def sample_matters(self):
        """Create sample matters for batch testing."""
        return [
            {
                "id": "matter-1",
                "title": "PI Case 1",
                "practice_area": "personal_injury",
                "jurisdiction": "TX",
                "metadata": {"incident_date": "2022-01-01", "case_type": "motor_vehicle_accident"}
            },
            {
                "id": "matter-2",
                "title": "PI Case 2",
                "practice_area": "personal_injury",
                "jurisdiction": "TX",
                "metadata": {"incident_date": "2022-02-01", "case_type": "slip_and_fall"}
            },
            {
                "id": "matter-3",
                "title": "PI Case 3",
                "practice_area": "personal_injury",
                "jurisdiction": "CA",
                "metadata": {"incident_date": "2022-03-01", "case_type": "medical_malpractice"}
            }
        ]

    @pytest.mark.asyncio
    async def test_process_batch_with_mcp_success(self, nodes, sample_matters):
        """Test successful batch processing with MCP."""
        mock_client = AsyncMock()
        mock_client.calculate_deadlines.return_value = {
            "deadlines": [
                {
                    "id": "deadline_1",
                    "name": "SOL Deadline",
                    "dueDate": "2024-01-01",
                    "priority": "high"
                }
            ],
            "jurisdiction": "TX",
            "triggerCode": "ACCIDENT_DATE"
        }

        batch_matters = sample_matters[:2]  # First 2 matters

        with patch.object(nodes, '_extract_sol_parameters') as mock_extract:
            mock_extract.return_value = {
                "jurisdiction": "TX",
                "trigger_code": "ACCIDENT_DATE",
                "start_date": "2022-01-01",
                "practice_area": "personal_injury"
            }

            results = await nodes._process_batch_with_mcp(batch_matters, mock_client, 1)

            assert results["processed_count"] == 2
            assert len(results["deadlines"]) == 2  # One deadline per matter
            assert len(results["errors"]) == 0
            assert mock_client.calculate_deadlines.call_count == 2

    @pytest.mark.asyncio
    async def test_process_batch_with_mcp_errors(self, nodes, sample_matters):
        """Test batch processing with MCP errors and retry logic."""
        mock_client = AsyncMock()

        # First call fails, second succeeds
        mock_client.calculate_deadlines.side_effect = [
            McpApiError("Temporary error", 503, "SERVICE_UNAVAILABLE"),
            McpApiError("Temporary error", 503, "SERVICE_UNAVAILABLE"),
            McpApiError("Temporary error", 503, "SERVICE_UNAVAILABLE"),
            {  # Success on retry for second matter
                "deadlines": [{"id": "deadline_1", "name": "SOL", "dueDate": "2024-01-01"}],
                "jurisdiction": "TX"
            }
        ]

        batch_matters = sample_matters[:2]

        with patch.object(nodes, '_extract_sol_parameters') as mock_extract:
            mock_extract.return_value = {
                "jurisdiction": "TX",
                "trigger_code": "ACCIDENT_DATE",
                "start_date": "2022-01-01",
                "practice_area": "personal_injury"
            }

            results = await nodes._process_batch_with_mcp(batch_matters, mock_client, 1)

            assert results["processed_count"] == 1  # Only second matter succeeded
            assert len(results["errors"]) == 1  # First matter failed after retries
            assert "Failed to process matter matter-1 after 3 attempts" in results["errors"][0]

    def test_detect_deadline_conflicts(self, nodes):
        """Test deadline conflict detection."""
        deadlines = [
            {"id": "deadline_1", "due_date": "2024-01-15", "title": "Deadline 1"},
            {"id": "deadline_2", "due_date": "2024-01-15", "title": "Deadline 2"},
            {"id": "deadline_3", "due_date": "2024-01-16", "title": "Deadline 3"}
        ]

        conflicts = nodes._detect_deadline_conflicts(deadlines)

        assert len(conflicts) == 1
        conflict = conflicts[0]
        assert conflict["type"] == "same_day_deadlines"
        assert conflict["date"] == "2024-01-15"
        assert len(conflict["deadline_ids"]) == 2
        assert conflict["severity"] == "medium"

    def test_generate_batch_recommendations(self, nodes):
        """Test batch processing recommendation generation."""
        recommendations = nodes._generate_batch_recommendations(
            processed_matters=10,
            total_deadlines=25,
            high_risk_count=3,
            errors=["Error 1", "Error 2"]
        )

        assert len(recommendations) > 0
        assert any("10 matters processed" in rec for rec in recommendations)
        assert any("25 deadlines calculated" in rec for rec in recommendations)
        assert any("URGENT: 3 high-risk deadlines" in rec for rec in recommendations)
        assert any("2 processing errors" in rec for rec in recommendations)


class TestErrorHandlingAndEdgeCases:
    """Test error handling and edge cases."""

    @pytest.fixture
    def nodes(self):
        """Create DeadlineInsightsNodes instance for testing."""
        return DeadlineInsightsNodes()

    @pytest.mark.asyncio
    async def test_track_sol_deadlines_mcp_disabled(self, nodes):
        """Test SOL tracking when MCP is disabled."""
        state = DeadlineInsightsState(
            tenant_id="test-tenant",
            analysis_type=AnalysisType.SOL_TRACKING
        )

        with patch('backend.agents.insights.deadline.nodes.ENABLE_MCP_INTEGRATION', False):
            result = await nodes.track_sol_deadlines(state)

            assert result.error == "MCP Rules Engine integration is disabled"
            assert result.status == "failed"

    @pytest.mark.asyncio
    async def test_track_sol_deadlines_no_tenant_id(self, nodes):
        """Test SOL tracking without tenant ID."""
        state = DeadlineInsightsState(
            tenant_id=None,
            analysis_type=AnalysisType.SOL_TRACKING
        )

        result = await nodes.track_sol_deadlines(state)

        assert result.error == "Tenant ID is required for SOL tracking"
        assert result.status == "failed"

    @pytest.mark.asyncio
    async def test_track_sol_deadlines_no_matters(self, nodes):
        """Test SOL tracking with no matters found."""
        state = DeadlineInsightsState(
            tenant_id="test-tenant",
            analysis_type=AnalysisType.SOL_TRACKING
        )

        with patch.object(nodes, '_get_repository') as mock_repo:
            with patch.object(nodes, '_fetch_matters_for_sol_tracking', new_callable=AsyncMock) as mock_fetch:
                mock_fetch.return_value = []

                result = await nodes.track_sol_deadlines(state)

                assert result.analysis is not None
                assert result.analysis.total_deadlines == 0
                assert "No matters found for SOL tracking" in result.analysis.recommendations
                assert result.status == "sol_tracked"

    def test_extract_sol_parameters_invalid_matter(self, nodes):
        """Test SOL parameter extraction with invalid matter data."""
        invalid_matter = {}

        params = nodes._extract_sol_parameters(invalid_matter)

        # Should still return some parameters with defaults
        assert params is not None
        assert params["jurisdiction"] == "TX"
        assert params["practice_area"] == "personal_injury"

    def test_is_high_risk_deadline_invalid_date(self, nodes):
        """Test high-risk detection with invalid date."""
        invalid_deadline = {"due_date": "invalid-date"}

        # Should not crash and return False
        assert nodes._is_high_risk_deadline(invalid_deadline) is False

        # Test with missing due_date
        missing_date_deadline = {}
        assert nodes._is_high_risk_deadline(missing_date_deadline) is False
