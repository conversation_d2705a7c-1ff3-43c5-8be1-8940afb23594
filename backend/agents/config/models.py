"""
Configuration Models for Centralized Agent System

This module defines Pydantic models for agent configuration, including
agent definitions, node configurations, prompt configurations, and
model selections.
"""

from typing import Dict, List, Optional, Any
from datetime import datetime, timezone
from enum import Enum
from pydantic import BaseModel, Field, field_validator


class AgentType(str, Enum):
    """Agent type enumeration."""
    INTERACTIVE = "interactive"
    INSIGHTS = "insights"
    SUPERVISOR = "supervisor"


class NodeType(str, Enum):
    """Node type enumeration."""
    ROUTER = "router"
    PROCESSOR = "processor"
    VALIDATOR = "validator"
    GENERATOR = "generator"
    CLASSIFIER = "classifier"
    EXECUTOR = "executor"


class PromptConfigModel(BaseModel):
    """Configuration model for agent prompts."""
    
    key: str = Field(description="Unique prompt key")
    content: str = Field(description="Prompt template content")
    description: Optional[str] = Field(None, description="Prompt description")
    tags: Optional[List[str]] = Field(None, description="Prompt tags")
    version: int = Field(1, description="Prompt version")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")
    is_active: bool = Field(True, description="Whether prompt is active")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")


class ModelConfigModel(BaseModel):
    """Configuration model for LLM models."""
    
    provider: str = Field(description="Model provider (e.g., openai, anthropic)")
    model_name: str = Field(description="Full model name (e.g., openai/gpt-4)")
    temperature: float = Field(0.2, ge=0.0, le=2.0, description="Model temperature")
    max_tokens: Optional[int] = Field(None, description="Maximum tokens")
    top_p: Optional[float] = Field(None, ge=0.0, le=1.0, description="Top-p sampling")
    frequency_penalty: Optional[float] = Field(None, ge=-2.0, le=2.0, description="Frequency penalty")
    presence_penalty: Optional[float] = Field(None, ge=-2.0, le=2.0, description="Presence penalty")
    
    @field_validator('model_name')
    @classmethod
    def validate_model_name(cls, v):
        """Validate model name format."""
        if '/' not in v:
            raise ValueError("Model name must be in format 'provider/model'")
        return v


class NodeConfigModel(BaseModel):
    """Configuration model for agent nodes."""
    
    name: str = Field(description="Node name")
    display_name: str = Field(description="Human-readable node name")
    description: Optional[str] = Field(None, description="Node description")
    node_type: NodeType = Field(description="Type of node")
    prompt_config: Optional[PromptConfigModel] = Field(None, description="Node-specific prompt")
    llm_config: Optional[ModelConfigModel] = Field(None, description="Node-specific model")
    tools: Optional[List[str]] = Field(None, description="Available tools for this node")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")
    is_enabled: bool = Field(True, description="Whether node is enabled")
    
    # Inheritance settings
    inherit_prompt_from_agent: bool = Field(True, description="Inherit prompt from agent")
    inherit_model_from_agent: bool = Field(True, description="Inherit model from agent")


class AgentConfigModel(BaseModel):
    """Comprehensive configuration model for agents."""
    
    # Basic agent information
    name: str = Field(description="Agent name")
    display_name: str = Field(description="Human-readable agent name")
    description: str = Field(description="Agent description")
    agent_type: AgentType = Field(description="Type of agent")
    version: str = Field("1.0.0", description="Agent version")
    icon: str = Field("🤖", description="Agent icon")
    
    # Configuration
    prompt_config: Optional[PromptConfigModel] = Field(None, description="Agent-level prompt")
    llm_config: Optional[ModelConfigModel] = Field(None, description="Agent-level model")
    nodes: Dict[str, NodeConfigModel] = Field(default_factory=dict, description="Agent nodes")
    
    # Capabilities and tools
    capabilities: List[str] = Field(default_factory=list, description="Agent capabilities")
    tools: List[str] = Field(default_factory=list, description="Available tools")
    
    # Environment and deployment
    environment: Optional[str] = Field(None, description="Target environment")
    tenant_id: Optional[str] = Field(None, description="Tenant ID (None for global)")
    
    # Status and metadata
    is_enabled: bool = Field(True, description="Whether agent is enabled")
    is_experimental: bool = Field(False, description="Whether agent is experimental")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")
    
    # Timestamps
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    
    # Testing and evaluation
    test_cases: Optional[List[Dict[str, Any]]] = Field(None, description="Test cases")
    evaluation_metrics: Optional[Dict[str, Any]] = Field(None, description="Evaluation metrics")


class EnvironmentConfigModel(BaseModel):
    """Configuration model for environment-specific settings."""
    
    name: str = Field(description="Environment name (dev/staging/prod)")
    description: str = Field(description="Environment description")
    
    # Default configurations
    default_llm_config: ModelConfigModel = Field(description="Default model configuration")
    default_prompt_settings: Dict[str, Any] = Field(default_factory=dict, description="Default prompt settings")
    
    # Environment-specific overrides
    agent_overrides: Dict[str, Dict[str, Any]] = Field(default_factory=dict, description="Agent-specific overrides")
    
    # Feature flags
    feature_flags: Dict[str, bool] = Field(default_factory=dict, description="Environment feature flags")
    
    # Resource limits
    max_tokens_per_request: int = Field(4000, description="Maximum tokens per request")
    max_requests_per_minute: int = Field(60, description="Rate limit per minute")
    timeout_seconds: int = Field(30, description="Request timeout")
    
    # Monitoring and logging
    enable_detailed_logging: bool = Field(False, description="Enable detailed logging")
    enable_metrics: bool = Field(True, description="Enable metrics collection")
    enable_tracing: bool = Field(False, description="Enable request tracing")


class TestCaseModel(BaseModel):
    """Model for agent test cases."""
    
    id: str = Field(description="Test case ID")
    name: str = Field(description="Test case name")
    description: str = Field(description="Test case description")
    agent_name: str = Field(description="Target agent name")
    node_name: Optional[str] = Field(None, description="Target node name")
    
    # Test input
    input_data: Dict[str, Any] = Field(description="Test input data")
    context: Optional[Dict[str, Any]] = Field(None, description="Test context")
    
    # Expected output
    expected_output: Optional[Dict[str, Any]] = Field(None, description="Expected output")
    success_criteria: List[str] = Field(description="Success criteria")
    
    # Test configuration
    timeout_seconds: int = Field(30, description="Test timeout")
    retry_count: int = Field(0, description="Number of retries")
    
    # Metadata
    tags: List[str] = Field(default_factory=list, description="Test tags")
    priority: str = Field("medium", description="Test priority")
    is_enabled: bool = Field(True, description="Whether test is enabled")


class EvaluationResultModel(BaseModel):
    """Model for evaluation results."""
    
    test_case_id: str = Field(description="Test case ID")
    agent_name: str = Field(description="Agent name")
    node_name: Optional[str] = Field(None, description="Node name")
    
    # Results
    success: bool = Field(description="Whether test passed")
    score: Optional[float] = Field(None, ge=0.0, le=1.0, description="Test score")
    output: Optional[Dict[str, Any]] = Field(None, description="Actual output")
    
    # Performance metrics
    execution_time_ms: int = Field(description="Execution time in milliseconds")
    token_usage: Optional[Dict[str, int]] = Field(None, description="Token usage")
    
    # Error information
    error_message: Optional[str] = Field(None, description="Error message if failed")
    error_type: Optional[str] = Field(None, description="Error type")
    
    # Metadata
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="Evaluation timestamp")
    environment: str = Field(description="Environment where test was run")
    configuration_hash: Optional[str] = Field(None, description="Configuration hash")
