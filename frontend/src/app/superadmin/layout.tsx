'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import SuperAdminGuard from '@/components/auth/SuperAdminGuard';
import {
  ChevronLeft,
  ChevronRight,
  LayoutDashboard,
  Users,
  Settings,
  BarChart3,
  Database,
  Shield,
  FileText,
  Brain,
  Calendar,
  Phone,
  Bot
} from 'lucide-react';

interface AdminLayoutProps {
  children: React.ReactNode;
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  const pathname = usePathname();
  const [collapsed, setCollapsed] = useState(false);

  const navItems = [
    {
      title: 'Dashboard',
      href: '/superadmin',
      icon: <LayoutDashboard className="h-5 w-5" />,
    },
    {
      title: 'Security',
      href: '/superadmin/security',
      icon: <Shield className="h-5 w-5" />,
    },
    {
      title: 'Users',
      href: '/superadmin/users',
      icon: <Users className="h-5 w-5" />,
    },
    {
      title: 'Tenant Quotas',
      href: '/superadmin/tenant-quotas',
      icon: <Shield className="h-5 w-5" />,
    },
    {
      title: 'Resource Usage',
      href: '/superadmin/resource-usage',
      icon: <BarChart3 className="h-5 w-5" />,
    },
    {
      title: 'Prompts',
      href: '/superadmin/prompts',
      icon: <FileText className="h-5 w-5" />,
    },
    {
      title: 'Models',
      href: '/superadmin/models',
      icon: <Brain className="h-5 w-5" />,
    },
    {
      title: 'Agent Config',
      href: '/superadmin/agents',
      icon: <Bot className="h-5 w-5" />,
    },
    {
      title: 'Calendar Health',
      href: '/superadmin/calendar',
      icon: <Calendar className="h-5 w-5" />,
    },
    {
      title: 'Voice Queues',
      href: '/superadmin/voice/queues',
      icon: <Phone className="h-5 w-5" />,
    },
    {
      title: 'Database',
      href: '/superadmin/database',
      icon: <Database className="h-5 w-5" />,
    },
    {
      title: 'Settings',
      href: '/superadmin/settings',
      icon: <Settings className="h-5 w-5" />,
    },
  ];

  return (
    <SuperAdminGuard fallbackPath="/dashboard">
      <div className="flex h-screen overflow-hidden">
        {/* Sidebar */}
        <div
          className={cn(
            "flex flex-col border-r bg-background transition-all duration-300",
            collapsed ? "w-16" : "w-64"
          )}
        >
          <div className="flex h-14 items-center border-b px-4">
            <Link href="/superadmin" className="flex items-center gap-2">
              <Shield className="h-6 w-6" />
              {!collapsed && <span className="font-semibold">Admin Portal</span>}
            </Link>
            <Button
              variant="ghost"
              size="icon"
              className="ml-auto"
              onClick={() => setCollapsed(!collapsed)}
            >
              {collapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
            </Button>
          </div>
          <ScrollArea className="flex-1">
            <nav className="flex flex-col gap-1 p-2">
              {navItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className={cn(
                    "flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium transition-colors",
                    pathname === item.href
                      ? "bg-accent text-accent-foreground"
                      : "hover:bg-accent hover:text-accent-foreground",
                    collapsed && "justify-center px-0"
                  )}
                >
                  {item.icon}
                  {!collapsed && <span>{item.title}</span>}
                </Link>
              ))}
            </nav>
          </ScrollArea>
        </div>

        {/* Main content */}
        <div className="flex flex-col flex-1 overflow-hidden">
          <main className="flex-1 overflow-y-auto">
            {children}
          </main>
        </div>
      </div>
    </SuperAdminGuard>
  );
}
