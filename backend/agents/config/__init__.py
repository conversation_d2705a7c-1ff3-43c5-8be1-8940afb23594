"""
Centralized Agent Configuration System

This package provides centralized configuration management for all AiLex agents,
including prompt management, model selection, environment-specific configurations,
and agent registry functionality.

Key Features:
- Centralized agent registry with automatic discovery
- Environment-specific configurations (dev/staging/prod)
- Integration with superadmin prompt and model management
- Agent testing and evaluation framework
- Configuration validation and hot-reloading

Usage:
    from backend.agents.config import AgentRegistry, get_agent_config
    
    # Get agent configuration
    config = get_agent_config("research_agent", tenant_id="tenant-123")
    
    # Get all available agents
    registry = AgentRegistry()
    agents = registry.get_all_agents()
"""

from .registry import AgentRegistry, get_agent_config, register_agent
from .environments import EnvironmentConfig, get_environment_config
from .models import AgentConfigModel, NodeConfigModel, PromptConfigModel
from .loader import ConfigLoader, load_agent_configs
from .validator import ConfigValidator, validate_agent_config
from .testing import AgentTester, TestSuite, EvaluationFramework

__all__ = [
    "AgentRegistry",
    "get_agent_config", 
    "register_agent",
    "EnvironmentConfig",
    "get_environment_config",
    "AgentConfigModel",
    "NodeConfigModel", 
    "PromptConfigModel",
    "ConfigLoader",
    "load_agent_configs",
    "ConfigValidator",
    "validate_agent_config",
    "AgentTester",
    "TestSuite",
    "EvaluationFramework"
]
