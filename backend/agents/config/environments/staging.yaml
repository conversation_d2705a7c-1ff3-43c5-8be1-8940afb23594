name: staging
description: Staging environment for pre-production testing

# Default model configuration for staging
default_model_config:
  provider: openai
  model_name: openai/gpt-4
  temperature: 0.2
  max_tokens: 3000

# Default prompt settings
default_prompt_settings:
  enable_debug_prompts: false
  include_reasoning: true
  verbose_responses: false

# Agent-specific overrides for staging
agent_overrides:
  research_agent:
    model_config:
      temperature: 0.25  # Slightly higher than production for testing
      max_tokens: 3500
    metadata:
      enable_caching: true
      cache_ttl_hours: 12
      enable_performance_monitoring: true
  
  intake_agent:
    model_config:
      temperature: 0.15  # Lower temperature for consistent intake
      max_tokens: 2500
    metadata:
      enable_validation_checks: true
      require_conflict_check: true
      enable_audit_logging: true
  
  supervisor_agent:
    model_config:
      temperature: 0.1  # Low temperature for consistent routing
      max_tokens: 1500
    metadata:
      enable_routing_analytics: true
      log_routing_decisions: true
      enable_fallback_testing: true

# Feature flags for staging
feature_flags:
  enable_debug_mode: false
  enable_experimental_features: true
  enable_mock_responses: false
  enable_detailed_tracing: true
  enable_prompt_debugging: false
  enable_model_switching: true
  enable_test_data_generation: false
  enable_performance_testing: true
  enable_load_testing: true
  enable_integration_testing: true

# Resource limits for staging
max_tokens_per_request: 3000
max_requests_per_minute: 60
timeout_seconds: 45

# Monitoring and logging
enable_detailed_logging: true
enable_metrics: true
enable_tracing: true

# Staging-specific settings
staging_settings:
  auto_reload_configs: false
  enable_hot_swapping: true
  mock_external_apis: false
  use_staging_database: true
  enable_cors: true
  debug_sql_queries: false
  enable_health_checks: true
  enable_smoke_tests: true
  enable_regression_tests: true
  test_data_refresh_hours: 24
