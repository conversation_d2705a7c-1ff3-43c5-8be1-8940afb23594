import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server-new';
import { isAuthenticatedLegacy as isAuthenticated, hasRoleLegacy as hasRole, UserRole } from '@/lib/auth/server-exports';
import { logSecurityEvent } from '@/lib/security/forensics';

// Define agent configuration interfaces
export interface AgentConfigResponse {
  name: string;
  display_name: string;
  description: string;
  agent_type: string;
  version: string;
  icon: string;
  capabilities: string[];
  nodes: NodeConfigResponse[];
  prompt_config?: PromptConfigResponse;
  model_config?: ModelConfigResponse;
  is_enabled: boolean;
  is_experimental: boolean;
  metadata?: Record<string, any>;
}

export interface NodeConfigResponse {
  name: string;
  display_name: string;
  description: string;
  node_type: string;
  prompt_config?: PromptConfigResponse;
  model_config?: ModelConfigResponse;
  is_enabled: boolean;
  inherit_prompt_from_agent: boolean;
  inherit_model_from_agent: boolean;
}

export interface PromptConfigResponse {
  key: string;
  content: string;
  description?: string;
  tags?: string[];
  version: number;
  is_active: boolean;
}

export interface ModelConfigResponse {
  provider: string;
  model_name: string;
  temperature: number;
  max_tokens?: number;
  top_p?: number;
}

/**
 * GET /api/admin/agent-configs
 * Get all agent configurations with their current settings
 */
export async function GET(request: NextRequest) {
  try {
    // Create a Supabase client
    const supabase = createClient();

    // Get the user from the session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      console.error('Session error:', sessionError);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = session.user;

    // Check if user is authenticated
    if (!isAuthenticated(user)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is a superadmin
    if (!hasRole(user, [UserRole.Superadmin])) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const agentType = searchParams.get('agent_type');
    const enabled = searchParams.get('enabled');
    const search = searchParams.get('search');

    // For now, return mock data based on the centralized configuration system
    // In a real implementation, this would load from the AgentRegistry
    const mockAgentConfigs: AgentConfigResponse[] = [
      {
        name: 'research_agent',
        display_name: 'Research Agent',
        description: 'Legal research agent for finding relevant laws, statutes, and case precedents',
        agent_type: 'interactive',
        version: '1.0.0',
        icon: '🔍',
        capabilities: ['legal_research', 'document_search', 'citation_formatting'],
        nodes: [
          {
            name: 'query_gen',
            display_name: 'Query Generation',
            description: 'Generate optimized search queries from user input',
            node_type: 'generator',
            is_enabled: true,
            inherit_prompt_from_agent: false,
            inherit_model_from_agent: false,
            prompt_config: {
              key: 'research_query_generation_prompt',
              content: 'You are a query generation specialist for legal research...',
              description: 'Prompt for generating optimized search queries',
              tags: ['query_generation', 'search'],
              version: 1,
              is_active: true
            },
            model_config: {
              provider: 'openai',
              model_name: 'openai/gpt-4',
              temperature: 0.4,
              max_tokens: 2000
            }
          },
          {
            name: 'rerank',
            display_name: 'Result Reranking',
            description: 'Rerank search results by relevance to user query',
            node_type: 'processor',
            is_enabled: true,
            inherit_prompt_from_agent: false,
            inherit_model_from_agent: false,
            model_config: {
              provider: 'openai',
              model_name: 'openai/gpt-4',
              temperature: 0.2
            }
          }
        ],
        prompt_config: {
          key: 'research_agent_system_prompt',
          content: 'You are a legal research assistant specialized in finding relevant laws...',
          description: 'System prompt for the research agent',
          tags: ['research', 'legal', 'system_prompt'],
          version: 1,
          is_active: true
        },
        model_config: {
          provider: 'openai',
          model_name: 'openai/gpt-4',
          temperature: 0.3,
          max_tokens: 3000
        },
        is_enabled: true,
        is_experimental: false,
        metadata: {
          created_by: 'system',
          last_updated: '2025-01-10'
        }
      },
      {
        name: 'intake_agent',
        display_name: 'Intake Agent',
        description: 'Multi-practice intake agent for Personal Injury, Family Law, and Criminal Defense cases',
        agent_type: 'interactive',
        version: '1.0.0',
        icon: '📝',
        capabilities: ['client_intake', 'case_classification', 'conflict_checking'],
        nodes: [
          {
            name: 'classify_practice_area',
            display_name: 'Practice Area Classification',
            description: 'Classify the case into appropriate practice area',
            node_type: 'classifier',
            is_enabled: true,
            inherit_prompt_from_agent: false,
            inherit_model_from_agent: false,
            model_config: {
              provider: 'anthropic',
              model_name: 'anthropic/claude-3-sonnet',
              temperature: 0.1
            }
          }
        ],
        prompt_config: {
          key: 'intake_agent_system_prompt',
          content: 'You are a professional legal intake specialist...',
          description: 'System prompt for the intake agent',
          tags: ['intake', 'legal', 'multi_practice'],
          version: 1,
          is_active: true
        },
        model_config: {
          provider: 'anthropic',
          model_name: 'anthropic/claude-3-sonnet',
          temperature: 0.2,
          max_tokens: 2500
        },
        is_enabled: true,
        is_experimental: false
      },
      {
        name: 'supervisor_agent',
        display_name: 'Supervisor Agent',
        description: 'Main routing and coordination agent for the AiLex platform',
        agent_type: 'insights',
        version: '1.0.0',
        icon: '👥',
        capabilities: ['intent_routing', 'task_coordination', 'agent_supervision'],
        nodes: [],
        model_config: {
          provider: 'openai',
          model_name: 'openai/gpt-4o',
          temperature: 0.2
        },
        is_enabled: true,
        is_experimental: false
      }
    ];

    // Apply filters
    let filteredConfigs = mockAgentConfigs;

    if (agentType) {
      filteredConfigs = filteredConfigs.filter(config => config.agent_type === agentType);
    }

    if (enabled !== null) {
      const isEnabled = enabled === 'true';
      filteredConfigs = filteredConfigs.filter(config => config.is_enabled === isEnabled);
    }

    if (search) {
      const searchLower = search.toLowerCase();
      filteredConfigs = filteredConfigs.filter(config =>
        config.name.toLowerCase().includes(searchLower) ||
        config.display_name.toLowerCase().includes(searchLower) ||
        config.description.toLowerCase().includes(searchLower) ||
        config.capabilities.some(cap => cap.toLowerCase().includes(searchLower))
      );
    }

    return NextResponse.json({
      agents: filteredConfigs,
      total: filteredConfigs.length,
      filters: {
        agent_type: agentType,
        enabled: enabled,
        search: search
      }
    });

  } catch (error) {
    console.error('Error in agent configs API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * POST /api/admin/agent-configs
 * Update agent configuration (prompt or model settings)
 */
export async function POST(request: NextRequest) {
  try {
    // Create a Supabase client
    const supabase = createClient();

    // Get the user from the session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      console.error('Session error:', sessionError);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = session.user;

    // Check if user is authenticated
    if (!isAuthenticated(user)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is a superadmin
    if (!hasRole(user, [UserRole.Superadmin])) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await request.json();
    const { 
      agent_name, 
      node_name, 
      config_type, 
      config_data,
      tenant_id = '*'
    } = body;

    // Validate required fields
    if (!agent_name || !config_type || !config_data) {
      return NextResponse.json({
        error: 'agent_name, config_type, and config_data are required'
      }, { status: 400 });
    }

    // Validate config_type
    if (!['prompt', 'model'].includes(config_type)) {
      return NextResponse.json({
        error: 'config_type must be either "prompt" or "model"'
      }, { status: 400 });
    }

    // For now, simulate saving the configuration
    // In a real implementation, this would:
    // 1. Update the agent configuration in the registry
    // 2. Save to database if needed
    // 3. Trigger configuration reload

    // Log the security event
    await logSecurityEvent(supabase, 'agent_config.updated', {
      agent_name,
      node_name,
      config_type,
      tenant_id,
      userId: user.id,
      userEmail: user.email,
      timestamp: new Date().toISOString()
    });

    return NextResponse.json({ 
      success: true, 
      message: `${config_type} configuration updated for ${agent_name}${node_name ? `/${node_name}` : ''}`,
      data: {
        agent_name,
        node_name,
        config_type,
        updated_at: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Error updating agent config:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
