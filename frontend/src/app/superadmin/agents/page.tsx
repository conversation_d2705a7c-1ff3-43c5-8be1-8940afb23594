'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Settings, 
  Play, 
  Pause, 
  TestTube, 
  Brain, 
  Search, 
  Filter,
  ChevronDown,
  ChevronRight,
  Edit,
  Save,
  X
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

// Import the agent configuration types
import type { AgentConfigResponse, NodeConfigResponse } from '@/app/api/admin/agent-configs/route';

interface AgentConfigPageProps {}

export default function AgentConfigPage({}: AgentConfigPageProps) {
  const [agents, setAgents] = useState<AgentConfigResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  const [filterEnabled, setFilterEnabled] = useState<string>('all');
  const [expandedAgents, setExpandedAgents] = useState<Set<string>>(new Set());
  const [editingConfig, setEditingConfig] = useState<{
    agentName: string;
    nodeName?: string;
    configType: 'prompt' | 'model';
  } | null>(null);
  
  const { toast } = useToast();

  useEffect(() => {
    loadAgentConfigs();
  }, [filterType, filterEnabled]);

  const loadAgentConfigs = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      
      if (filterType !== 'all') {
        params.append('agent_type', filterType);
      }
      if (filterEnabled !== 'all') {
        params.append('enabled', filterEnabled);
      }
      if (searchTerm) {
        params.append('search', searchTerm);
      }

      const response = await fetch(`/api/admin/agent-configs?${params}`);
      if (!response.ok) {
        throw new Error('Failed to load agent configurations');
      }

      const data = await response.json();
      setAgents(data.agents || []);
    } catch (error) {
      console.error('Error loading agent configs:', error);
      toast({
        title: 'Error',
        description: 'Failed to load agent configurations',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const toggleAgentExpansion = (agentName: string) => {
    const newExpanded = new Set(expandedAgents);
    if (newExpanded.has(agentName)) {
      newExpanded.delete(agentName);
    } else {
      newExpanded.add(agentName);
    }
    setExpandedAgents(newExpanded);
  };

  const handleConfigEdit = (agentName: string, nodeName?: string, configType: 'prompt' | 'model' = 'model') => {
    setEditingConfig({ agentName, nodeName, configType });
  };

  const handleConfigSave = async (configData: any) => {
    if (!editingConfig) return;

    try {
      const response = await fetch('/api/admin/agent-configs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          agent_name: editingConfig.agentName,
          node_name: editingConfig.nodeName,
          config_type: editingConfig.configType,
          config_data: configData
        })
      });

      if (!response.ok) {
        throw new Error('Failed to save configuration');
      }

      toast({
        title: 'Success',
        description: 'Configuration saved successfully'
      });

      setEditingConfig(null);
      loadAgentConfigs(); // Reload to show updated config
    } catch (error) {
      console.error('Error saving config:', error);
      toast({
        title: 'Error',
        description: 'Failed to save configuration',
        variant: 'destructive'
      });
    }
  };

  const filteredAgents = agents.filter(agent => {
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      return (
        agent.name.toLowerCase().includes(searchLower) ||
        agent.display_name.toLowerCase().includes(searchLower) ||
        agent.description.toLowerCase().includes(searchLower) ||
        agent.capabilities.some(cap => cap.toLowerCase().includes(searchLower))
      );
    }
    return true;
  });

  const getAgentTypeColor = (type: string) => {
    switch (type) {
      case 'interactive': return 'bg-blue-100 text-blue-800';
      case 'insights': return 'bg-purple-100 text-purple-800';
      case 'supervisor': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getNodeTypeColor = (type: string) => {
    switch (type) {
      case 'router': return 'bg-orange-100 text-orange-800';
      case 'processor': return 'bg-blue-100 text-blue-800';
      case 'generator': return 'bg-green-100 text-green-800';
      case 'classifier': return 'bg-purple-100 text-purple-800';
      case 'validator': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading agent configurations...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Agent Configuration</h1>
          <p className="text-muted-foreground mt-1">
            Manage agent prompts, models, and testing configurations
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => window.open('/superadmin/agents/testing', '_blank')}>
            <TestTube className="h-4 w-4 mr-2" />
            Testing Sandbox
          </Button>
          <Button onClick={loadAgentConfigs}>
            <Settings className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 items-end">
            <div className="flex-1">
              <label className="text-sm font-medium mb-2 block">Search</label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search agents, capabilities, or descriptions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">Agent Type</label>
              <Select value={filterType} onValueChange={setFilterType}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="interactive">Interactive</SelectItem>
                  <SelectItem value="insights">Insights</SelectItem>
                  <SelectItem value="supervisor">Supervisor</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">Status</label>
              <Select value={filterEnabled} onValueChange={setFilterEnabled}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All</SelectItem>
                  <SelectItem value="true">Enabled</SelectItem>
                  <SelectItem value="false">Disabled</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Agent List */}
      <div className="space-y-4">
        {filteredAgents.map((agent) => (
          <Card key={agent.name} className="overflow-hidden">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => toggleAgentExpansion(agent.name)}
                    className="p-1"
                  >
                    {expandedAgents.has(agent.name) ? (
                      <ChevronDown className="h-4 w-4" />
                    ) : (
                      <ChevronRight className="h-4 w-4" />
                    )}
                  </Button>
                  <div className="text-2xl">{agent.icon}</div>
                  <div>
                    <CardTitle className="text-lg">{agent.display_name}</CardTitle>
                    <CardDescription className="text-sm">
                      {agent.name} • v{agent.version}
                    </CardDescription>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge className={getAgentTypeColor(agent.agent_type)}>
                    {agent.agent_type}
                  </Badge>
                  <Badge variant={agent.is_enabled ? 'default' : 'secondary'}>
                    {agent.is_enabled ? 'Enabled' : 'Disabled'}
                  </Badge>
                  {agent.is_experimental && (
                    <Badge variant="outline">Experimental</Badge>
                  )}
                </div>
              </div>
            </CardHeader>

            {expandedAgents.has(agent.name) && (
              <CardContent className="pt-0">
                <div className="space-y-4">
                  {/* Description */}
                  <p className="text-sm text-muted-foreground">{agent.description}</p>

                  {/* Capabilities */}
                  <div>
                    <h4 className="font-medium mb-2">Capabilities</h4>
                    <div className="flex flex-wrap gap-1">
                      {agent.capabilities.map((capability) => (
                        <Badge key={capability} variant="outline" className="text-xs">
                          {capability.replace('_', ' ')}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* Agent-level Configuration */}
                  <div className="border rounded-lg p-4 bg-muted/50">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-medium">Agent Configuration</h4>
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleConfigEdit(agent.name, undefined, 'prompt')}
                        >
                          <Edit className="h-3 w-3 mr-1" />
                          Edit Prompt
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleConfigEdit(agent.name, undefined, 'model')}
                        >
                          <Brain className="h-3 w-3 mr-1" />
                          Edit Model
                        </Button>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium">Model:</span>{' '}
                        {agent.model_config?.model_name || 'Not configured'}
                      </div>
                      <div>
                        <span className="font-medium">Temperature:</span>{' '}
                        {agent.model_config?.temperature || 'Default'}
                      </div>
                      <div>
                        <span className="font-medium">Prompt:</span>{' '}
                        {agent.prompt_config?.key || 'Not configured'}
                      </div>
                      <div>
                        <span className="font-medium">Max Tokens:</span>{' '}
                        {agent.model_config?.max_tokens || 'Default'}
                      </div>
                    </div>
                  </div>

                  {/* Nodes */}
                  {agent.nodes.length > 0 && (
                    <div>
                      <h4 className="font-medium mb-3">Nodes ({agent.nodes.length})</h4>
                      <div className="space-y-3">
                        {agent.nodes.map((node) => (
                          <div key={node.name} className="border rounded-lg p-3 bg-background">
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center gap-2">
                                <h5 className="font-medium">{node.display_name}</h5>
                                <Badge className={getNodeTypeColor(node.node_type)} variant="outline">
                                  {node.node_type}
                                </Badge>
                                {!node.is_enabled && (
                                  <Badge variant="secondary">Disabled</Badge>
                                )}
                              </div>
                              <div className="flex gap-1">
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => handleConfigEdit(agent.name, node.name, 'prompt')}
                                >
                                  <Edit className="h-3 w-3" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => handleConfigEdit(agent.name, node.name, 'model')}
                                >
                                  <Brain className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>
                            
                            <p className="text-xs text-muted-foreground mb-2">{node.description}</p>
                            
                            <div className="grid grid-cols-2 gap-2 text-xs">
                              <div>
                                <span className="font-medium">Model:</span>{' '}
                                {node.model_config?.model_name || 
                                 (node.inherit_model_from_agent ? 'Inherited' : 'Not configured')}
                              </div>
                              <div>
                                <span className="font-medium">Prompt:</span>{' '}
                                {node.prompt_config?.key || 
                                 (node.inherit_prompt_from_agent ? 'Inherited' : 'Not configured')}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            )}
          </Card>
        ))}
      </div>

      {filteredAgents.length === 0 && (
        <Card>
          <CardContent className="text-center py-8">
            <p className="text-muted-foreground">No agents found matching your criteria.</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
