"""
Environment Configuration System

This module handles environment-specific configurations for agents,
supporting development, staging, and production environments with
appropriate defaults and overrides.
"""

import os
import logging
from typing import Dict, List, Optional, Any
from enum import Enum
from pathlib import Path

from .models import EnvironmentConfigModel, ModelConfigModel, AgentConfigModel
from .loader import ConfigLoader

logger = logging.getLogger(__name__)


class Environment(str, Enum):
    """Environment enumeration."""
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"
    TESTING = "testing"


class EnvironmentConfig:
    """
    Manages environment-specific configurations for agents.
    
    Provides different defaults and overrides for development,
    staging, and production environments.
    """
    
    def __init__(self):
        """Initialize environment configuration."""
        self.config_loader = ConfigLoader()
        self.current_environment = self._detect_environment()
        self._environment_configs = {}
        
        # Load environment configurations
        self._load_environment_configs()
    
    def _detect_environment(self) -> Environment:
        """
        Detect current environment from environment variables.
        
        Returns:
            Current environment
        """
        env_name = os.getenv('AILEX_ENVIRONMENT', 'development').lower()
        
        # Map common environment variable values
        env_mapping = {
            'dev': Environment.DEVELOPMENT,
            'development': Environment.DEVELOPMENT,
            'local': Environment.DEVELOPMENT,
            'stage': Environment.STAGING,
            'staging': Environment.STAGING,
            'prod': Environment.PRODUCTION,
            'production': Environment.PRODUCTION,
            'test': Environment.TESTING,
            'testing': Environment.TESTING
        }
        
        return env_mapping.get(env_name, Environment.DEVELOPMENT)
    
    def _load_environment_configs(self) -> None:
        """Load all environment configurations."""
        for env in Environment:
            config_data = self.config_loader.load_environment_config(env.value)
            if config_data:
                try:
                    self._environment_configs[env] = EnvironmentConfigModel(**config_data)
                except Exception as e:
                    logger.error(f"Failed to load environment config for {env.value}: {e}")
                    # Create default config
                    self._environment_configs[env] = self._create_default_environment_config(env)
            else:
                # Create default config
                self._environment_configs[env] = self._create_default_environment_config(env)
    
    def _create_default_environment_config(self, env: Environment) -> EnvironmentConfigModel:
        """
        Create default configuration for an environment.
        
        Args:
            env: Environment to create config for
            
        Returns:
            Default environment configuration
        """
        if env == Environment.DEVELOPMENT:
            return EnvironmentConfigModel(
                name="development",
                description="Development environment with verbose logging and relaxed limits",
                default_model_config=ModelConfigModel(
                    provider="openai",
                    model_name="openai/gpt-3.5-turbo",
                    temperature=0.3
                ),
                feature_flags={
                    "enable_debug_mode": True,
                    "enable_experimental_features": True,
                    "enable_mock_responses": True
                },
                max_tokens_per_request=2000,
                max_requests_per_minute=100,
                timeout_seconds=60,
                enable_detailed_logging=True,
                enable_metrics=True,
                enable_tracing=True
            )
        
        elif env == Environment.STAGING:
            return EnvironmentConfigModel(
                name="staging",
                description="Staging environment for pre-production testing",
                default_model_config=ModelConfigModel(
                    provider="openai",
                    model_name="openai/gpt-4",
                    temperature=0.2
                ),
                feature_flags={
                    "enable_debug_mode": False,
                    "enable_experimental_features": True,
                    "enable_mock_responses": False
                },
                max_tokens_per_request=4000,
                max_requests_per_minute=60,
                timeout_seconds=45,
                enable_detailed_logging=True,
                enable_metrics=True,
                enable_tracing=False
            )
        
        elif env == Environment.PRODUCTION:
            return EnvironmentConfigModel(
                name="production",
                description="Production environment with optimized performance and monitoring",
                default_model_config=ModelConfigModel(
                    provider="openai",
                    model_name="openai/gpt-4",
                    temperature=0.1
                ),
                feature_flags={
                    "enable_debug_mode": False,
                    "enable_experimental_features": False,
                    "enable_mock_responses": False
                },
                max_tokens_per_request=4000,
                max_requests_per_minute=30,
                timeout_seconds=30,
                enable_detailed_logging=False,
                enable_metrics=True,
                enable_tracing=False
            )
        
        else:  # TESTING
            return EnvironmentConfigModel(
                name="testing",
                description="Testing environment with mocked responses and fast execution",
                default_model_config=ModelConfigModel(
                    provider="openai",
                    model_name="openai/gpt-3.5-turbo",
                    temperature=0.0
                ),
                feature_flags={
                    "enable_debug_mode": True,
                    "enable_experimental_features": False,
                    "enable_mock_responses": True
                },
                max_tokens_per_request=1000,
                max_requests_per_minute=1000,
                timeout_seconds=10,
                enable_detailed_logging=False,
                enable_metrics=False,
                enable_tracing=False
            )
    
    def get_current_environment(self) -> Environment:
        """Get the current environment."""
        return self.current_environment
    
    def get_environment_config(self, env: Optional[Environment] = None) -> EnvironmentConfigModel:
        """
        Get configuration for an environment.
        
        Args:
            env: Environment to get config for (defaults to current)
            
        Returns:
            Environment configuration
        """
        if env is None:
            env = self.current_environment
        
        return self._environment_configs.get(env, self._create_default_environment_config(env))
    
    def apply_environment_overrides(
        self, 
        agent_config: AgentConfigModel, 
        env: Optional[Environment] = None
    ) -> AgentConfigModel:
        """
        Apply environment-specific overrides to agent configuration.
        
        Args:
            agent_config: Base agent configuration
            env: Environment to apply overrides for
            
        Returns:
            Agent configuration with environment overrides applied
        """
        if env is None:
            env = self.current_environment
        
        env_config = self.get_environment_config(env)
        
        # Create a copy of the agent config
        config_dict = agent_config.dict()
        
        # Apply environment overrides
        if agent_config.name in env_config.agent_overrides:
            overrides = env_config.agent_overrides[agent_config.name]
            self._deep_merge_dict(config_dict, overrides)
        
        # Apply default model config if agent doesn't have one
        if not config_dict.get('model_config'):
            config_dict['model_config'] = env_config.default_model_config.dict()
        
        # Set environment
        config_dict['environment'] = env.value
        
        # Apply environment-specific metadata
        if 'metadata' not in config_dict:
            config_dict['metadata'] = {}
        
        config_dict['metadata'].update({
            'environment': env.value,
            'max_tokens_per_request': env_config.max_tokens_per_request,
            'max_requests_per_minute': env_config.max_requests_per_minute,
            'timeout_seconds': env_config.timeout_seconds,
            'feature_flags': env_config.feature_flags
        })
        
        return AgentConfigModel(**config_dict)
    
    def _deep_merge_dict(self, base: Dict[str, Any], override: Dict[str, Any]) -> None:
        """
        Deep merge override dictionary into base dictionary.
        
        Args:
            base: Base dictionary to merge into
            override: Override dictionary to merge from
        """
        for key, value in override.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                self._deep_merge_dict(base[key], value)
            else:
                base[key] = value
    
    def get_feature_flag(self, flag_name: str, env: Optional[Environment] = None) -> bool:
        """
        Get feature flag value for environment.
        
        Args:
            flag_name: Name of the feature flag
            env: Environment to check (defaults to current)
            
        Returns:
            Feature flag value
        """
        if env is None:
            env = self.current_environment
        
        env_config = self.get_environment_config(env)
        return env_config.feature_flags.get(flag_name, False)
    
    def is_development(self) -> bool:
        """Check if current environment is development."""
        return self.current_environment == Environment.DEVELOPMENT
    
    def is_staging(self) -> bool:
        """Check if current environment is staging."""
        return self.current_environment == Environment.STAGING
    
    def is_production(self) -> bool:
        """Check if current environment is production."""
        return self.current_environment == Environment.PRODUCTION
    
    def is_testing(self) -> bool:
        """Check if current environment is testing."""
        return self.current_environment == Environment.TESTING
    
    def create_environment_configs(self) -> None:
        """Create default environment configuration files."""
        environments_dir = self.config_loader.environments_config_dir
        
        for env in Environment:
            config_file = environments_dir / f"{env.value}.yaml"
            if not config_file.exists():
                try:
                    default_config = self._create_default_environment_config(env)
                    config_data = default_config.dict(exclude_none=True)
                    
                    import yaml
                    with open(config_file, 'w') as f:
                        yaml.dump(config_data, f, default_flow_style=False, indent=2)
                    
                    logger.info(f"Created default environment config: {env.value}")
                except Exception as e:
                    logger.error(f"Failed to create environment config for {env.value}: {e}")


# Global environment config instance
_env_config = None


def get_environment_config() -> EnvironmentConfig:
    """Get the global environment configuration instance."""
    global _env_config
    if _env_config is None:
        _env_config = EnvironmentConfig()
    return _env_config


def get_current_environment() -> Environment:
    """Get the current environment."""
    return get_environment_config().get_current_environment()


def apply_environment_overrides(agent_config: AgentConfigModel) -> AgentConfigModel:
    """Apply environment overrides to agent configuration."""
    return get_environment_config().apply_environment_overrides(agent_config)


def get_feature_flag(flag_name: str) -> bool:
    """Get feature flag value for current environment."""
    return get_environment_config().get_feature_flag(flag_name)
