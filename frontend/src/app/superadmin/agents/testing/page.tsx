'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Play, 
  TestTube, 
  Plus, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  BarChart3,
  FileText,
  Settings
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

// Import types
import type { TestResult, TestCaseRequest } from '@/app/api/admin/agent-testing/route';

interface TestExecution {
  id: string;
  agent_name: string;
  node_name?: string;
  status: 'running' | 'completed' | 'failed';
  started_at: string;
  completed_at?: string;
  results: TestResult[];
}

export default function AgentTestingPage() {
  const [agents, setAgents] = useState<string[]>([]);
  const [testCases, setTestCases] = useState<any[]>([]);
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [executions, setExecutions] = useState<TestExecution[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedAgent, setSelectedAgent] = useState<string>('');
  const [selectedNode, setSelectedNode] = useState<string>('');
  const [testInput, setTestInput] = useState<string>('');
  const [expectedOutput, setExpectedOutput] = useState<string>('');
  const [newTestCase, setNewTestCase] = useState<Partial<TestCaseRequest>>({
    name: '',
    description: '',
    agent_name: '',
    input_data: {},
    success_criteria: [],
    timeout_seconds: 30,
    priority: 'medium'
  });

  const { toast } = useToast();

  useEffect(() => {
    loadTestingData();
  }, []);

  const loadTestingData = async () => {
    try {
      setLoading(true);
      
      // Load test cases and results
      const response = await fetch('/api/admin/agent-testing?include_results=true');
      if (!response.ok) {
        throw new Error('Failed to load testing data');
      }

      const data = await response.json();
      setTestCases(data.test_cases || []);
      setTestResults(data.test_results || []);
      
      // Extract unique agent names
      const agentNames = [...new Set(data.test_cases?.map((tc: any) => tc.agent_name) || [])] as string[];
      setAgents(agentNames);

      if (agentNames.length > 0 && !selectedAgent) {
        setSelectedAgent(agentNames[0]);
      }
    } catch (error) {
      console.error('Error loading testing data:', error);
      toast({
        title: 'Error',
        description: 'Failed to load testing data',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const executeQuickTest = async () => {
    if (!selectedAgent || !testInput.trim()) {
      toast({
        title: 'Error',
        description: 'Please select an agent and provide test input',
        variant: 'destructive'
      });
      return;
    }

    try {
      const inputData = JSON.parse(testInput);
      const expectedData = expectedOutput ? JSON.parse(expectedOutput) : undefined;

      const response = await fetch('/api/admin/agent-testing', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'execute_tests',
          agent_name: selectedAgent,
          node_name: selectedNode || undefined,
          test_case_ids: [`quick_test_${Date.now()}`],
          environment: 'sandbox'
        })
      });

      if (!response.ok) {
        throw new Error('Failed to execute test');
      }

      const result = await response.json();
      
      // Add to executions
      const newExecution: TestExecution = {
        id: result.execution_id,
        agent_name: selectedAgent,
        node_name: selectedNode || undefined,
        status: 'completed',
        started_at: result.started_at,
        completed_at: new Date().toISOString(),
        results: result.results
      };
      
      setExecutions(prev => [newExecution, ...prev]);
      setTestResults(prev => [...result.results, ...prev]);

      toast({
        title: 'Success',
        description: `Test executed successfully. ${result.summary.passed_tests}/${result.summary.total_tests} tests passed.`
      });

    } catch (error) {
      console.error('Error executing test:', error);
      toast({
        title: 'Error',
        description: 'Failed to execute test. Check your input format.',
        variant: 'destructive'
      });
    }
  };

  const createTestCase = async () => {
    if (!newTestCase.name || !newTestCase.agent_name) {
      toast({
        title: 'Error',
        description: 'Please provide test case name and select an agent',
        variant: 'destructive'
      });
      return;
    }

    try {
      const response = await fetch('/api/admin/agent-testing', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'create_test_case',
          ...newTestCase
        })
      });

      if (!response.ok) {
        throw new Error('Failed to create test case');
      }

      const result = await response.json();
      setTestCases(prev => [result.test_case, ...prev]);
      
      // Reset form
      setNewTestCase({
        name: '',
        description: '',
        agent_name: '',
        input_data: {},
        success_criteria: [],
        timeout_seconds: 30,
        priority: 'medium'
      });

      toast({
        title: 'Success',
        description: 'Test case created successfully'
      });

    } catch (error) {
      console.error('Error creating test case:', error);
      toast({
        title: 'Error',
        description: 'Failed to create test case',
        variant: 'destructive'
      });
    }
  };

  const getStatusIcon = (status: string, success?: boolean) => {
    switch (status) {
      case 'running':
        return <Clock className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'completed':
        return success ? 
          <CheckCircle className="h-4 w-4 text-green-500" /> : 
          <XCircle className="h-4 w-4 text-red-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getSuccessRate = () => {
    if (testResults.length === 0) return 0;
    return (testResults.filter(r => r.success).length / testResults.length) * 100;
  };

  const getAverageExecutionTime = () => {
    if (testResults.length === 0) return 0;
    return testResults.reduce((sum, r) => sum + r.execution_time_ms, 0) / testResults.length;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading testing environment...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Agent Testing Sandbox</h1>
          <p className="text-muted-foreground mt-1">
            Test and evaluate agent performance in a safe environment
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={loadTestingData}>
            <Settings className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <TestTube className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-sm font-medium">Total Tests</p>
                <p className="text-2xl font-bold">{testResults.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm font-medium">Success Rate</p>
                <p className="text-2xl font-bold">{getSuccessRate().toFixed(1)}%</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Clock className="h-5 w-5 text-orange-500" />
              <div>
                <p className="text-sm font-medium">Avg Response Time</p>
                <p className="text-2xl font-bold">{(getAverageExecutionTime() / 1000).toFixed(1)}s</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-purple-500" />
              <div>
                <p className="text-sm font-medium">Active Agents</p>
                <p className="text-2xl font-bold">{agents.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="quick-test" className="space-y-4">
        <TabsList>
          <TabsTrigger value="quick-test">Quick Test</TabsTrigger>
          <TabsTrigger value="test-cases">Test Cases</TabsTrigger>
          <TabsTrigger value="results">Results</TabsTrigger>
          <TabsTrigger value="create-test">Create Test</TabsTrigger>
        </TabsList>

        {/* Quick Test Tab */}
        <TabsContent value="quick-test">
          <Card>
            <CardHeader>
              <CardTitle>Quick Test</CardTitle>
              <CardDescription>
                Run a quick test against any agent with custom input
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">Agent</label>
                  <Select value={selectedAgent} onValueChange={setSelectedAgent}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select agent" />
                    </SelectTrigger>
                    <SelectContent>
                      {agents.map(agent => (
                        <SelectItem key={agent} value={agent}>
                          {agent.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 block">Node (Optional)</label>
                  <Input
                    placeholder="e.g., query_gen, rerank"
                    value={selectedNode}
                    onChange={(e) => setSelectedNode(e.target.value)}
                  />
                </div>
              </div>
              
              <div>
                <label className="text-sm font-medium mb-2 block">Test Input (JSON)</label>
                <Textarea
                  placeholder='{"message": "What is the statute of limitations for personal injury in Texas?"}'
                  value={testInput}
                  onChange={(e) => setTestInput(e.target.value)}
                  rows={4}
                />
              </div>
              
              <div>
                <label className="text-sm font-medium mb-2 block">Expected Output (JSON, Optional)</label>
                <Textarea
                  placeholder='{"type": "research_response", "contains": ["statute", "Texas"]}'
                  value={expectedOutput}
                  onChange={(e) => setExpectedOutput(e.target.value)}
                  rows={3}
                />
              </div>
              
              <Button onClick={executeQuickTest} className="w-full">
                <Play className="h-4 w-4 mr-2" />
                Run Test
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Test Cases Tab */}
        <TabsContent value="test-cases">
          <Card>
            <CardHeader>
              <CardTitle>Test Cases</CardTitle>
              <CardDescription>
                Manage and execute predefined test cases
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {testCases.map((testCase) => (
                  <div key={testCase.id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div>
                        <h4 className="font-medium">{testCase.name}</h4>
                        <p className="text-sm text-muted-foreground">{testCase.description}</p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{testCase.agent_name}</Badge>
                        <Badge variant={testCase.priority === 'high' ? 'destructive' : 'secondary'}>
                          {testCase.priority}
                        </Badge>
                        <Button size="sm" variant="outline">
                          <Play className="h-3 w-3 mr-1" />
                          Run
                        </Button>
                      </div>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Tags: {testCase.tags?.join(', ') || 'None'}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Results Tab */}
        <TabsContent value="results">
          <Card>
            <CardHeader>
              <CardTitle>Test Results</CardTitle>
              <CardDescription>
                View recent test execution results and performance metrics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {testResults.slice(0, 10).map((result, index) => (
                  <div key={`${result.test_case_id}-${index}`} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        {getStatusIcon('completed', result.success)}
                        <div>
                          <h4 className="font-medium">{result.test_case_id}</h4>
                          <p className="text-sm text-muted-foreground">
                            {result.agent_name}{result.node_name ? ` / ${result.node_name}` : ''}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium">
                          {result.execution_time_ms}ms
                        </div>
                        {result.score && (
                          <div className="text-xs text-muted-foreground">
                            Score: {(result.score * 100).toFixed(1)}%
                          </div>
                        )}
                      </div>
                    </div>
                    
                    {result.error_message && (
                      <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
                        {result.error_message}
                      </div>
                    )}
                    
                    {result.token_usage && (
                      <div className="mt-2 text-xs text-muted-foreground">
                        Tokens: {result.token_usage.input} in, {result.token_usage.output} out
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Create Test Tab */}
        <TabsContent value="create-test">
          <Card>
            <CardHeader>
              <CardTitle>Create Test Case</CardTitle>
              <CardDescription>
                Create a new test case for systematic agent evaluation
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">Test Name</label>
                  <Input
                    placeholder="e.g., Basic Research Test"
                    value={newTestCase.name}
                    onChange={(e) => setNewTestCase(prev => ({ ...prev, name: e.target.value }))}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 block">Agent</label>
                  <Select 
                    value={newTestCase.agent_name} 
                    onValueChange={(value) => setNewTestCase(prev => ({ ...prev, agent_name: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select agent" />
                    </SelectTrigger>
                    <SelectContent>
                      {agents.map(agent => (
                        <SelectItem key={agent} value={agent}>
                          {agent.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div>
                <label className="text-sm font-medium mb-2 block">Description</label>
                <Textarea
                  placeholder="Describe what this test validates..."
                  value={newTestCase.description}
                  onChange={(e) => setNewTestCase(prev => ({ ...prev, description: e.target.value }))}
                  rows={2}
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">Priority</label>
                  <Select 
                    value={newTestCase.priority} 
                    onValueChange={(value: any) => setNewTestCase(prev => ({ ...prev, priority: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 block">Timeout (seconds)</label>
                  <Input
                    type="number"
                    value={newTestCase.timeout_seconds}
                    onChange={(e) => setNewTestCase(prev => ({ ...prev, timeout_seconds: parseInt(e.target.value) }))}
                  />
                </div>
              </div>
              
              <Button onClick={createTestCase} className="w-full">
                <Plus className="h-4 w-4 mr-2" />
                Create Test Case
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
