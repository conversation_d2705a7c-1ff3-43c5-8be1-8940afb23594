"""
Basic Tests for MCP Integration (No External Dependencies)

This module contains basic tests for the MCP integration that don't require
external dependencies like Google Cloud SDK or aiohttp.
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock

from backend.agents.insights.deadline.mcp_client import (
    Mcp<PERSON>lient, 
    <PERSON>cp<PERSON><PERSON><PERSON>rror, 
    CircuitBreakerState
)
from backend.agents.insights.deadline.nodes import DeadlineInsightsNodes
from backend.agents.insights.deadline.state import (
    DeadlineInsightsState,
    AnalysisType,
    BatchProcessingConfig
)


class TestMcpClientBasic:
    """Test basic MCP client functionality without external dependencies."""
    
    def test_client_initialization(self):
        """Test MCP client initialization."""
        client = McpClient(
            base_url="https://test.example.com",
            api_key="test-key",
            timeout=30,
            max_retries=3
        )
        
        assert client.base_url == "https://test.example.com"
        assert client.api_key == "test-key"
        assert client.timeout == 30
        assert client.max_retries == 3
        assert client.circuit_breaker_state == CircuitBreakerState.CLOSED
    
    def test_circuit_breaker_state(self):
        """Test circuit breaker state tracking."""
        client = McpClient("https://test.example.com", "test-key")
        
        state = client.get_circuit_breaker_state()
        assert state["state"] == "closed"
        assert state["consecutiveFailures"] == 0
        assert state["lastFailureTime"] == 0
    
    def test_record_failure(self):
        """Test failure recording and circuit breaker state changes."""
        client = McpClient("https://test.example.com", "test-key")
        
        # Record failures
        for i in range(5):
            client._record_failure()
        
        # Should open circuit breaker after 5 failures
        assert client.circuit_breaker_state == CircuitBreakerState.OPEN
        assert client.consecutive_failures == 5
        assert client.last_failure_time > 0


class TestSolTrackingLogicBasic:
    """Test SOL tracking logic without external dependencies."""
    
    @pytest.fixture
    def nodes(self):
        """Create DeadlineInsightsNodes instance for testing."""
        return DeadlineInsightsNodes()
    
    @pytest.fixture
    def sample_matter(self):
        """Create sample matter data for testing."""
        return {
            "id": "matter-1",
            "title": "Personal Injury Case",
            "practice_area": "personal_injury",
            "work_type": "litigation",
            "jurisdiction": "TX",
            "filing_date": "2023-01-15",
            "opened_date": "2023-01-10",
            "metadata": {
                "incident_date": "2022-12-01",
                "case_type": "motor_vehicle_accident"
            }
        }
    
    def test_extract_sol_parameters(self, nodes, sample_matter):
        """Test SOL parameter extraction from matter data."""
        params = nodes._extract_sol_parameters(sample_matter)
        
        assert params is not None
        assert params["jurisdiction"] == "TX"
        assert params["trigger_code"] == "ACCIDENT_DATE"
        assert params["start_date"] == "2022-12-01"
        assert params["practice_area"] == "personal_injury"
    
    def test_extract_sol_parameters_missing_data(self, nodes):
        """Test SOL parameter extraction with missing data."""
        incomplete_matter = {
            "id": "matter-1",
            "title": "Incomplete Matter"
        }
        
        params = nodes._extract_sol_parameters(incomplete_matter)
        
        # Should still return parameters with defaults
        assert params is not None
        assert params["jurisdiction"] == "TX"  # Default
        assert params["practice_area"] == "personal_injury"  # Default
    
    def test_map_practice_area(self, nodes):
        """Test practice area mapping."""
        assert nodes._map_practice_area("personal_injury") == "personal_injury"
        assert nodes._map_practice_area("family_law") == "family_law"
        assert nodes._map_practice_area("unknown_area") == "personal_injury"  # Default
    
    def test_determine_trigger_code(self, nodes, sample_matter):
        """Test trigger code determination logic."""
        # Test motor vehicle accident
        trigger = nodes._determine_trigger_code(sample_matter)
        assert trigger == "ACCIDENT_DATE"
        
        # Test medical malpractice
        sample_matter["metadata"]["case_type"] = "medical_malpractice"
        trigger = nodes._determine_trigger_code(sample_matter)
        assert trigger == "DISCOVERY_DATE"
        
        # Test wrongful death
        sample_matter["metadata"]["case_type"] = "wrongful_death"
        trigger = nodes._determine_trigger_code(sample_matter)
        assert trigger == "DEATH_DATE"
        
        # Test default for unknown case type
        sample_matter["metadata"]["case_type"] = "unknown_type"
        trigger = nodes._determine_trigger_code(sample_matter)
        assert trigger == "ACCIDENT_DATE"  # Default for personal injury
        
        # Test criminal defense
        sample_matter["practice_area"] = "criminal_defense"
        trigger = nodes._determine_trigger_code(sample_matter)
        assert trigger == "CHARGE_DATE"
    
    def test_determine_sol_start_date(self, nodes, sample_matter):
        """Test SOL start date determination."""
        start_date = nodes._determine_sol_start_date(sample_matter)
        assert start_date == "2022-12-01"  # From incident_date
        
        # Test fallback to filing_date
        del sample_matter["metadata"]["incident_date"]
        start_date = nodes._determine_sol_start_date(sample_matter)
        assert start_date == "2023-01-15"  # From filing_date
        
        # Test fallback to opened_date
        del sample_matter["filing_date"]
        start_date = nodes._determine_sol_start_date(sample_matter)
        assert start_date == "2023-01-10"  # From opened_date
        
        # Test fallback when no dates available
        del sample_matter["opened_date"]
        start_date = nodes._determine_sol_start_date(sample_matter)
        assert start_date is not None  # Should return fallback date
        assert len(start_date) == 10  # YYYY-MM-DD format
    
    def test_process_mcp_response(self, nodes, sample_matter):
        """Test MCP response processing."""
        mcp_response = {
            "deadlines": [
                {
                    "id": "deadline_1",
                    "name": "SOL Deadline",
                    "dueDate": "2024-12-01",
                    "priority": "high",
                    "category": "statute_of_limitations",
                    "description": "Personal injury SOL",
                    "legalBasis": "Texas Civil Practice Code",
                    "consequences": "Case time-barred"
                }
            ],
            "jurisdiction": "TX",
            "triggerCode": "ACCIDENT_DATE",
            "calculatedAt": "2023-01-15T10:00:00Z"
        }
        
        deadlines = nodes._process_mcp_response(sample_matter, mcp_response)
        
        assert len(deadlines) == 1
        deadline = deadlines[0]
        assert deadline["matter_id"] == "matter-1"
        assert deadline["title"] == "SOL Deadline"
        assert deadline["due_date"] == "2024-12-01"
        assert deadline["priority"] == "high"
        assert deadline["source"] == "mcp_rules_engine"
        assert deadline["metadata"]["matter_title"] == "Personal Injury Case"
    
    def test_is_high_risk_deadline(self, nodes):
        """Test high-risk deadline detection."""
        from datetime import datetime, timedelta
        
        # High-risk deadline (within 90 days)
        future_date = (datetime.now() + timedelta(days=30)).isoformat()
        high_risk_deadline = {"due_date": future_date}
        assert nodes._is_high_risk_deadline(high_risk_deadline) is True
        
        # Low-risk deadline (more than 90 days)
        far_future_date = (datetime.now() + timedelta(days=120)).isoformat()
        low_risk_deadline = {"due_date": far_future_date}
        assert nodes._is_high_risk_deadline(low_risk_deadline) is False
        
        # Past deadline
        past_date = (datetime.now() - timedelta(days=30)).isoformat()
        past_deadline = {"due_date": past_date}
        assert nodes._is_high_risk_deadline(past_deadline) is False
        
        # Invalid date
        invalid_deadline = {"due_date": "invalid-date"}
        assert nodes._is_high_risk_deadline(invalid_deadline) is False
        
        # Missing due_date
        missing_date_deadline = {}
        assert nodes._is_high_risk_deadline(missing_date_deadline) is False
    
    def test_generate_sol_recommendations(self, nodes):
        """Test SOL recommendation generation."""
        sol_results = [
            {
                "id": "deadline_1",
                "jurisdiction": "TX",
                "metadata": {"practice_area": "personal_injury"}
            },
            {
                "id": "deadline_2", 
                "jurisdiction": "CA",
                "metadata": {"practice_area": "medical_malpractice"}
            }
        ]
        high_risk_deadlines = ["deadline_1"]
        
        recommendations = nodes._generate_sol_recommendations(sol_results, high_risk_deadlines)
        
        assert len(recommendations) > 0
        assert any("URGENT" in rec for rec in recommendations)
        assert any("Personal injury cases" in rec for rec in recommendations)
        assert any("Multi-jurisdiction" in rec for rec in recommendations)
        assert any("Monitoring 2 statute of limitations deadlines" in rec for rec in recommendations)
        
        # Test with no results
        empty_recommendations = nodes._generate_sol_recommendations([], [])
        assert len(empty_recommendations) > 0
        assert any("No SOL deadlines calculated" in rec for rec in empty_recommendations)


class TestBatchProcessingBasic:
    """Test basic batch processing functionality."""
    
    @pytest.fixture
    def nodes(self):
        """Create DeadlineInsightsNodes instance for testing."""
        return DeadlineInsightsNodes()
    
    def test_detect_deadline_conflicts(self, nodes):
        """Test deadline conflict detection."""
        deadlines = [
            {"id": "deadline_1", "due_date": "2024-01-15", "title": "Deadline 1"},
            {"id": "deadline_2", "due_date": "2024-01-15", "title": "Deadline 2"},
            {"id": "deadline_3", "due_date": "2024-01-16", "title": "Deadline 3"}
        ]
        
        conflicts = nodes._detect_deadline_conflicts(deadlines)
        
        assert len(conflicts) == 1
        conflict = conflicts[0]
        assert conflict["type"] == "same_day_deadlines"
        assert conflict["date"] == "2024-01-15"
        assert len(conflict["deadline_ids"]) == 2
        assert conflict["severity"] == "medium"
        assert "2 deadlines due on the same day" in conflict["description"]
        
        # Test with no conflicts
        no_conflict_deadlines = [
            {"id": "deadline_1", "due_date": "2024-01-15"},
            {"id": "deadline_2", "due_date": "2024-01-16"}
        ]
        no_conflicts = nodes._detect_deadline_conflicts(no_conflict_deadlines)
        assert len(no_conflicts) == 0
    
    def test_generate_batch_recommendations(self, nodes):
        """Test batch processing recommendation generation."""
        recommendations = nodes._generate_batch_recommendations(
            processed_matters=10,
            total_deadlines=25,
            high_risk_count=3,
            errors=["Error 1", "Error 2"]
        )
        
        assert len(recommendations) > 0
        assert any("10 matters processed" in rec for rec in recommendations)
        assert any("25 deadlines calculated" in rec for rec in recommendations)
        assert any("URGENT: 3 high-risk deadlines" in rec for rec in recommendations)
        assert any("2 processing errors" in rec for rec in recommendations)
        
        # Test with high error rate
        high_error_recommendations = nodes._generate_batch_recommendations(
            processed_matters=10,
            total_deadlines=5,
            high_risk_count=0,
            errors=["Error " + str(i) for i in range(5)]  # 50% error rate
        )
        assert any("High error rate detected" in rec for rec in high_error_recommendations)
        
        # Test with large dataset
        large_dataset_recommendations = nodes._generate_batch_recommendations(
            processed_matters=100,
            total_deadlines=200,
            high_risk_count=10,
            errors=[]
        )
        assert any("Large dataset processed" in rec for rec in large_dataset_recommendations)
