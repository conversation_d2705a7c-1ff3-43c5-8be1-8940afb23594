name: intake_agent
display_name: Intake Agent
description: Multi-practice intake agent for Personal Injury, Family Law, and Criminal Defense cases
agent_type: interactive
version: 1.0.0
icon: 📝

# Agent-level prompt configuration
prompt_config:
  key: intake_agent_system_prompt
  content: |
    You are a professional legal intake specialist for a multi-practice law firm.
    
    Your role is to:
    - Conduct initial client consultations
    - Gather essential case information
    - Classify cases by practice area (Personal Injury, Family Law, Criminal Defense)
    - Assess case urgency and potential conflicts
    - Guide clients through the intake process with empathy and professionalism
    
    Practice Areas:
    1. Personal Injury: Auto accidents, slip/fall, medical malpractice, wrongful death
    2. Family Law: Divorce, custody, adoption, domestic relations
    3. Criminal Defense: DUI, assault, theft, drug charges, white collar crimes
    
    Always maintain:
    - Professional and empathetic tone
    - Client confidentiality
    - Accurate information gathering
    - Clear communication about next steps
    
    If a case falls outside our practice areas, politely explain and suggest appropriate referrals.
  description: System prompt for the intake agent
  tags: ["intake", "legal", "multi_practice", "system_prompt"]
  version: 1
  is_active: true

# Agent-level model configuration
llm_config:
  provider: anthropic
  model_name: anthropic/claude-3-sonnet
  temperature: 0.2
  max_tokens: 2500

# Node configurations
nodes:
  classify_practice_area:
    name: classify_practice_area
    display_name: Practice Area Classification
    description: Classify the case into appropriate practice area
    node_type: classifier
    prompt_config:
      key: intake_classification_prompt
      content: |
        You are a case classification specialist.
        
        Based on the client's description, classify the case into one of these practice areas:
        - PERSONAL_INJURY: Auto accidents, slip/fall, medical malpractice, wrongful death, product liability
        - FAMILY_LAW: Divorce, child custody, adoption, domestic relations, prenuptial agreements
        - CRIMINAL_DEFENSE: DUI, assault, theft, drug charges, white collar crimes, traffic violations
        - OTHER: Cases outside our practice areas
        
        Consider:
        1. Primary legal issues involved
        2. Type of incident or dispute
        3. Parties involved
        4. Potential legal remedies
        
        Respond with the practice area and a brief explanation.
      description: Prompt for classifying cases by practice area
      tags: ["classification", "practice_area"]
      version: 1
    llm_config:
      provider: anthropic
      model_name: anthropic/claude-3-sonnet
      temperature: 0.1
    inherit_prompt_from_agent: false
    inherit_model_from_agent: false
  
  collect_case_details:
    name: collect_case_details
    display_name: Case Details Collection
    description: Collect detailed information about the case
    node_type: processor
    prompt_config:
      key: intake_details_collection_prompt
      content: |
        You are a case details collection specialist.
        
        Your task is to gather comprehensive information about the client's case.
        
        For Personal Injury cases, collect:
        - Date, time, and location of incident
        - Description of what happened
        - Injuries sustained
        - Medical treatment received
        - Insurance information
        - Witnesses
        
        For Family Law cases, collect:
        - Type of family matter
        - Parties involved (spouse, children)
        - Assets and debts
        - Current living arrangements
        - Previous legal actions
        
        For Criminal Defense cases, collect:
        - Charges or allegations
        - Date of incident/arrest
        - Law enforcement contact
        - Court dates
        - Bail status
        
        Ask follow-up questions to gather complete information while being sensitive to the client's situation.
      description: Prompt for collecting detailed case information
      tags: ["details", "information_gathering"]
      version: 1
    llm_config:
      provider: anthropic
      model_name: anthropic/claude-3-sonnet
      temperature: 0.3
    inherit_prompt_from_agent: false
    inherit_model_from_agent: false
  
  assess_urgency:
    name: assess_urgency
    display_name: Urgency Assessment
    description: Assess the urgency level of the case
    node_type: validator
    prompt_config:
      key: intake_urgency_assessment_prompt
      content: |
        You are a case urgency assessment specialist.
        
        Evaluate the urgency of the case based on:
        
        HIGH URGENCY:
        - Statute of limitations concerns
        - Pending court dates
        - Active criminal charges
        - Emergency custody issues
        - Serious ongoing harm
        
        MEDIUM URGENCY:
        - Recent incidents (within 30 days)
        - Insurance claim deadlines
        - Ongoing negotiations
        - Time-sensitive evidence
        
        LOW URGENCY:
        - General consultations
        - Preventive legal planning
        - Non-time-sensitive matters
        
        Provide urgency level (HIGH/MEDIUM/LOW) with explanation and recommended timeline for attorney contact.
      description: Prompt for assessing case urgency
      tags: ["urgency", "assessment", "prioritization"]
      version: 1
    llm_config:
      provider: anthropic
      model_name: anthropic/claude-3-sonnet
      temperature: 0.1
    inherit_prompt_from_agent: false
    inherit_model_from_agent: false
  
  check_conflicts:
    name: check_conflicts
    display_name: Conflict Check
    description: Perform initial conflict of interest screening
    node_type: validator
    prompt_config:
      key: intake_conflict_check_prompt
      content: |
        You are a conflict of interest screening specialist.
        
        Perform initial conflict screening by identifying:
        
        1. All parties involved in the matter
        2. Opposing parties or adverse interests
        3. Related entities (employers, insurance companies, etc.)
        4. Previous or ongoing legal matters
        5. Family relationships between parties
        
        Flag potential conflicts if:
        - We may have represented opposing parties
        - There are business relationships with adverse parties
        - Multiple parties have conflicting interests
        - Attorney has personal relationships with parties
        
        Provide a conflict assessment (CLEAR/POTENTIAL_CONFLICT/REQUIRES_REVIEW) with explanation.
      description: Prompt for conflict of interest screening
      tags: ["conflicts", "screening", "ethics"]
      version: 1
    llm_config:
      provider: anthropic
      model_name: anthropic/claude-3-sonnet
      temperature: 0.0
    inherit_prompt_from_agent: false
    inherit_model_from_agent: false

# Agent capabilities
capabilities:
  - client_intake
  - case_classification
  - conflict_checking
  - urgency_assessment
  - multi_practice_support
  - information_gathering
  - client_communication

# Available tools
tools:
  - create_client_record
  - create_matter_record
  - schedule_consultation
  - send_intake_summary
  - check_calendar_availability

# Testing configuration
test_cases:
  - id: personal_injury_intake_test
    name: Personal Injury Intake Test
    description: Test intake process for personal injury case
    input_data:
      message: "I was in a car accident last week and injured my back. The other driver ran a red light."
    expected_output:
      practice_area: "PERSONAL_INJURY"
      urgency: "MEDIUM"
    success_criteria:
      - "Correctly classifies as personal injury"
      - "Gathers accident details"
      - "Assesses appropriate urgency"
    timeout_seconds: 45
    tags: ["intake", "personal_injury", "classification"]
    priority: high
    is_enabled: true
  
  - id: family_law_intake_test
    name: Family Law Intake Test
    description: Test intake process for family law case
    input_data:
      message: "I need help with a divorce. My spouse and I have two children and own a house together."
    expected_output:
      practice_area: "FAMILY_LAW"
      urgency: "MEDIUM"
    success_criteria:
      - "Correctly classifies as family law"
      - "Identifies key family law issues"
      - "Gathers relevant details"
    timeout_seconds: 45
    tags: ["intake", "family_law", "divorce"]
    priority: high
    is_enabled: true

# Evaluation metrics
evaluation_metrics:
  classification_accuracy_weight: 0.3
  information_completeness_weight: 0.3
  urgency_assessment_weight: 0.2
  client_satisfaction_weight: 0.2

# Metadata
metadata:
  created_by: system
  last_updated: "2025-01-10"
  documentation_url: "https://docs.ailex.com/agents/intake"
  support_contact: "<EMAIL>"
  practice_areas: ["personal_injury", "family_law", "criminal_defense"]

# Status flags
is_enabled: true
is_experimental: false
